"""
Notification service for Meeting Intelligence Agent.

Streamlined for Post meetingworkflow - Email notifications only.
Uses client email template for professional formatting.
"""

import asyncio
import logging
import json
import re
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)


class NotificationChannel(Enum):
    """Supported notification channels."""
    EMAIL = "email"


class NotificationPriority(Enum):
    """Notification priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class NotificationRequest:
    """Request for sending a notification."""
    channel: NotificationChannel
    recipients: List[str]
    subject: str
    content: str
    priority: NotificationPriority = NotificationPriority.MEDIUM
    attachments: Optional[List[str]] = None
    html_content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class NotificationResult:
    """Result of notification delivery."""
    success: bool
    channel: NotificationChannel
    message: str
    delivered_to: List[str]
    failed_recipients: List[str] = None
    error: Optional[str] = None
    timestamp: Optional[str] = None

    def __post_init__(self):
        if self.failed_recipients is None:
            self.failed_recipients = []
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


class NotificationService:
    """
    Streamlined notification service for the Post meetingworkflow.
    
    Handles email notifications using client email template for professional formatting.
    """
    
    def __init__(self):
        """Initialize the notification service."""
        self.email_service = None
        self.template_cache = {}
        self._initialize_services()
        
        logger.info("NotificationService initialized - Email only")
    
    def _initialize_services(self):
        """Initialize email service."""
        try:
            # Lazy import to avoid circular dependencies
            from src.services.email_service import EmailService
            self.email_service = EmailService()
            logger.info("Email service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize email service: {e}")
            self.email_service = None
    
    def _load_template(self, template_name: str) -> str:
        """Load email template from client folder."""
        if template_name in self.template_cache:
            return self.template_cache[template_name]
        
        try:
            # Get project root (go up from src/services to project root)
            project_root = Path(__file__).parent.parent.parent
            template_path = project_root / "client" / "email_templates" / f"{template_name}.html"
            
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                    self.template_cache[template_name] = template_content
                    return template_content
            else:
                logger.error(f"Template not found: {template_path}")
                return self._get_fallback_template()
        except Exception as e:
            logger.error(f"Failed to load template {template_name}: {e}")
            return self._get_fallback_template()
    
    def _get_fallback_template(self) -> str:
        """Get fallback template if main template fails to load."""
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Meeting Summary - {{title}}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: white; padding: 32px; border-radius: 8px; }
        h1 { color: #1a1a1a; }
        h2 { color: #1a1a1a; border-bottom: 2px solid #e9ecef; padding-bottom: 8px; }
        .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 16px 0; }
        .outcome { background: #f8f9fa; padding: 16px; margin: 8px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>{{title}}</h1>
        <p>Generated on {{date_processed}}</p>
        <h2>Summary</h2>
        <div class="summary">{{executive_summary}}</div>
        <h2>Key Outcomes</h2>
        {{#outcomes}}
        <div class="outcome">
            <strong>{{decision}}</strong> ({{owner}})<br>
            {{rationale}}
        </div>
        {{/outcomes}}
    </div>
</body>
</html>
        """
    
    def _render_template(self, template: str, data: Dict[str, Any]) -> str:
        """
        Simple template renderer for Mustache-style templates.
        Handles basic {{variable}} substitution and {{#array}} loops.
        """
        try:
            # Handle simple variable substitution
            def replace_variable(match):
                var_name = match.group(1)
                if var_name in data:
                    value = data[var_name]
                    if isinstance(value, str):
                        return value
                    elif isinstance(value, list):
                        return ', '.join(str(item) for item in value)
                    else:
                        return str(value)
                return match.group(0)  # Return original if not found
            
            # Replace simple variables like {{title}}
            result = re.sub(r'\{\{([^#/][^}]*)\}\}', replace_variable, template)
            
            # Handle array loops like {{#outcomes}}...{{/outcomes}}
            def replace_array_loop(match):
                array_name = match.group(1)
                loop_content = match.group(2)
                
                if array_name in data and isinstance(data[array_name], list):
                    array_items = data[array_name]
                    if not array_items:
                        # Handle empty array case
                        empty_pattern = rf'\{{\^{array_name}\}}(.*?)\{{\/{array_name}\}}'
                        empty_match = re.search(empty_pattern, template, re.DOTALL)
                        if empty_match:
                            return empty_match.group(1)
                        return ""
                    
                    rendered_items = []
                    for item in array_items:
                        item_html = loop_content
                        if isinstance(item, dict):
                            # Replace variables in the loop content
                            for key, value in item.items():
                                if isinstance(value, str):
                                    item_html = item_html.replace(f'{{{{{key}}}}}', value)
                                else:
                                    item_html = item_html.replace(f'{{{{{key}}}}}', str(value))
                        rendered_items.append(item_html)
                    return ''.join(rendered_items)
                else:
                    # Handle empty array case
                    empty_pattern = rf'\{{\^{array_name}\}}(.*?)\{{\/{array_name}\}}'
                    empty_match = re.search(empty_pattern, template, re.DOTALL)
                    if empty_match:
                        return empty_match.group(1)
                    return ""
            
            # Replace array loops like {{#outcomes}}...{{/outcomes}}
            result = re.sub(r'\{\{#([^}]+)\}\}(.*?)\{\{\/\1\}\}', replace_array_loop, result, flags=re.DOTALL)
            
            # Remove empty array blocks like {{^outcomes}}...{{/outcomes}}
            result = re.sub(r'\{\{\^[^}]+\}\}.*?\{\{\/[^}]+\}\}', '', result, flags=re.DOTALL)
            
            return result
            
        except Exception as e:
            logger.error(f"Template rendering failed: {e}")
            return f"<p>Error rendering template: {str(e)}</p>"
    
    async def send_notification(self, request: NotificationRequest) -> NotificationResult:
        """
        Send a notification through the specified channel.
        
        Args:
            request: Notification request details
            
        Returns:
            NotificationResult with delivery status
        """
        try:
            if request.channel == NotificationChannel.EMAIL:
                return await self._send_email_notification(request)
            else:
                return NotificationResult(
                    success=False,
                    channel=request.channel,
                    message=f"Unsupported notification channel: {request.channel.value}",
                    delivered_to=[],
                    error=f"Channel {request.channel.value} not supported"
                )
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return NotificationResult(
                success=False,
                channel=request.channel,
                message=f"Notification failed: {str(e)}",
                delivered_to=[],
                error=str(e)
            )
    
    async def _send_email_notification(self, request: NotificationRequest) -> NotificationResult:
        """Send email notification."""
        if not self.email_service:
            return NotificationResult(
                success=False,
                channel=NotificationChannel.EMAIL,
                message="Email service not initialized",
                delivered_to=[],
                error="Email service unavailable"
            )
        
        try:
            # Send email to all recipients
            delivered_to = []
            failed_recipients = []
            
            for recipient in request.recipients:
                try:
                    result = await self.email_service.send_email(
                        to_email=recipient,
                        subject=request.subject,
                        body=request.content,
                        html_body=request.html_content,
                        attachments=request.attachments
                    )
                    
                    if result.get("success", False):
                        delivered_to.append(recipient)
                    else:
                        failed_recipients.append(recipient)
                        
                except Exception as e:
                    logger.error(f"Failed to send email to {recipient}: {e}")
                    failed_recipients.append(recipient)
            
            success = len(delivered_to) > 0
            message = f"Email sent to {len(delivered_to)} recipients"
            if failed_recipients:
                message += f", failed for {len(failed_recipients)} recipients"
            
            return NotificationResult(
                success=success,
                channel=NotificationChannel.EMAIL,
                message=message,
                delivered_to=delivered_to,
                failed_recipients=failed_recipients
            )
            
        except Exception as e:
            logger.error(f"Email notification failed: {e}")
            return NotificationResult(
                success=False,
                channel=NotificationChannel.EMAIL,
                message=f"Email notification failed: {str(e)}",
                delivered_to=[],
                error=str(e)
            )
    
    async def send_meeting_summary(self, 
                                 attendees: List[str], 
                                 meeting_title: str,
                                 json_summary: Dict[str, Any],
                                 html_summary: Optional[str] = None) -> NotificationResult:
        """
        Send meeting summary to attendees via email using client template.
        
        Args:
            attendees: List of attendee email addresses
            meeting_title: Title of the meeting
            json_summary: JSON summary data with template variables
            html_summary: Optional pre-rendered HTML (will be generated if not provided)
            
        Returns:
            NotificationResult with delivery status
        """
        try:
            # Create email subject
            subject = f"Meeting Summary: {meeting_title}"
            
            # Load and render HTML template
            if not html_summary:
                template = self._load_template("meeting_summary_template")
                
                # Prepare template data
                template_data = {
                    "title": meeting_title,
                    "meeting_title": meeting_title,
                    "date_processed": datetime.now().strftime("%B %d, %Y"),
                    **json_summary  # Include all JSON data
                }
                
                # Render the template
                html_summary = self._render_template(template, template_data)
            
            # Create plain text content from JSON
            plain_content = f"""
Meeting Summary: {meeting_title}

{json_summary.get('executive_summary', 'No summary available')}

Key Outcomes:
""" + "\n".join(f"• {outcome.get('decision', 'No decision')}" for outcome in json_summary.get('outcomes', []))
            
            # Add open questions if present
            if json_summary.get('open_questions'):
                plain_content += "\n\nOpen Questions:\n"
                for question in json_summary.get('open_questions', []):
                    plain_content += f"• {question.get('question', 'No question')} (Owner: {question.get('owner', 'Unassigned')})\n"
            
            # Add working sessions if present
            if json_summary.get('working_sessions_needed'):
                plain_content += "\n\nWorking Sessions Needed:\n"
                for session in json_summary.get('working_sessions_needed', []):
                    plain_content += f"• {session.get('topic', 'No topic')} - {session.get('goal', 'No goal')}\n"
            
            # Create notification request
            request = NotificationRequest(
                channel=NotificationChannel.EMAIL,
                recipients=attendees,
                subject=subject,
                content=plain_content,
                html_content=html_summary,
                priority=NotificationPriority.MEDIUM,
                metadata={
                    "meeting_title": meeting_title,
                    "summary_type": "meeting_intelligence",
                    "generated_at": datetime.now().isoformat(),
                    "template_used": "meeting_summary_template"
                }
            )
            
            # Send the notification
            result = await self.send_notification(request)
            
            logger.info(f"Meeting summary sent to {len(result.delivered_to)} attendees using client template")
            return result
            
        except Exception as e:
            logger.error(f"Failed to send meeting summary: {e}")
            return NotificationResult(
                success=False,
                channel=NotificationChannel.EMAIL,
                message=f"Meeting summary delivery failed: {str(e)}",
                delivered_to=[],
                error=str(e)
            )
    
    async def send_admin_alert(self, 
                              subject: str, 
                              message: str,
                              admin_emails: List[str] = None) -> NotificationResult:
        """
        Send admin alert for errors or important events.
        
        Args:
            subject: Alert subject
            message: Alert message
            admin_emails: List of admin email addresses
            
        Returns:
            NotificationResult with delivery status
        """
        try:
            # Default admin emails if not provided
            if not admin_emails:
                admin_emails = ["<EMAIL>"]  # This should be configurable
            
            request = NotificationRequest(
                channel=NotificationChannel.EMAIL,
                recipients=admin_emails,
                subject=f"[ADMIN ALERT] {subject}",
                content=message,
                priority=NotificationPriority.HIGH,
                metadata={
                    "alert_type": "admin",
                    "generated_at": datetime.now().isoformat()
                }
            )
            
            result = await self.send_notification(request)
            
            logger.info(f"Admin alert sent to {len(result.delivered_to)} administrators")
            return result
            
        except Exception as e:
            logger.error(f"Failed to send admin alert: {e}")
            return NotificationResult(
                success=False,
                channel=NotificationChannel.EMAIL,
                message=f"Admin alert delivery failed: {str(e)}",
                delivered_to=[],
                error=str(e)
            )
    
    async def check_health(self) -> Dict[str, Any]:
        """Check the health of notification services."""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {}
        }
        
        # Check email service
        if self.email_service:
            try:
                email_healthy = await self.email_service.check_health()
                health_status["services"]["email"] = "healthy" if email_healthy else "unhealthy"
            except Exception as e:
                health_status["services"]["email"] = f"error: {str(e)}"
                health_status["status"] = "degraded"
        else:
            health_status["services"]["email"] = "not_available"
            health_status["status"] = "degraded"
        
        return health_status
    
    def get_supported_channels(self) -> List[str]:
        """Get list of supported notification channels."""
        return [NotificationChannel.EMAIL.value]
    
    def get_service_info(self) -> Dict[str, Any]:
        """Get information about the notification service."""
        return {
            "service": "NotificationService",
            "version": "2.0.0",
            "description": "Streamlined notification service for Post meeting intelligence workflow",
            "supported_channels": self.get_supported_channels(),
            "features": [
                "Email notifications",
                "Meeting summary distribution",
                "Admin alerts",
                "HTML email support"
            ],
            "status": "active",
            "timestamp": datetime.now().isoformat()
        }
