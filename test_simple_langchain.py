#!/usr/bin/env python3
"""
Simple test to verify <PERSON><PERSON>hai<PERSON> integration.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """Test basic imports."""
    print("🔍 Testing imports...")
    
    try:
        from src.services.ai_summarizer import AISummarizer
        print("✅ AISummarizer imported successfully")
        
        from langchain_google_vertexai import ChatVertexAI
        print("✅ ChatVertexAI imported successfully")
        
        from langchain_core.messages import HumanMessage, SystemMessage
        print("✅ LangChain messages imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_summarizer_init():
    """Test AISummarizer initialization."""
    print("\n🤖 Testing AISummarizer initialization...")
    
    try:
        from src.services.ai_summarizer import AISummarizer
        
        summarizer = AISummarizer()
        print("✅ AISummarizer initialized")
        
        if hasattr(summarizer, 'langchain_llm') and summarizer.langchain_llm:
            print("✅ LangChain ChatVertexAI model available")
        else:
            print("⚠️ LangChain ChatVertexAI model not available")
            
        if hasattr(summarizer, 'gemini_model') and summarizer.gemini_model:
            print("✅ Gemini fallback model available")
        else:
            print("⚠️ Gemini fallback model not available")
            
        return True
    except Exception as e:
        print(f"❌ Summarizer initialization error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_call():
    """Test a simple AI call."""
    print("\n📞 Testing simple AI call...")
    
    try:
        from src.services.ai_summarizer import AISummarizer
        
        summarizer = AISummarizer()
        
        # Simple test prompt
        test_prompt = "Please respond with 'Hello from LangChain!' to confirm the connection is working."
        
        response = summarizer._call_ai_model(test_prompt)
        
        if response:
            print("✅ AI call successful")
            print(f"   Response: {response[:100]}...")
            return True
        else:
            print("❌ AI call returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ AI call error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run simple tests."""
    print("🧪 Simple LangChain Integration Test")
    print("=" * 40)
    
    tests = [
        ("Imports", test_imports),
        ("Summarizer Init", test_summarizer_init),
        ("Simple AI Call", test_simple_call),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 40)
    print("📊 TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 LangChain integration is working!")
    else:
        print("⚠️ Some tests failed.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
