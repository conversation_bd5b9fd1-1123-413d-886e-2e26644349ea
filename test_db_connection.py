import os
import sys
from pathlib import Path

# Set up environment
os.environ['PYTHONPATH'] = './meeting-intelligence-agent'

def test_db_connection():
    """Test database connection with detailed error reporting."""
    try:
        from src.configuration.config import DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME, DB_URL
        
        print("🔍 DATABASE CONNECTION TEST")
        print("=" * 50)
        print(f"Host: {DB_HOST}")
        print(f"Port: {DB_PORT}")
        print(f"Database: {DB_NAME}")
        print(f"User: {DB_USER}")
        print(f"Password: {'*' * len(DB_PASSWORD) if DB_PASSWORD else 'None'}")
        print(f"URL: {DB_URL}")
        print("=" * 50)
        
        # Test basic connectivity first
        import socket
        print(f"\n🌐 Testing network connectivity to {DB_HOST}:{DB_PORT}...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((DB_HOST, int(DB_PORT)))
            sock.close()
            
            if result == 0:
                print("✅ Network connectivity: SUCCESS")
            else:
                print(f"❌ Network connectivity: FAILED (Error code: {result})")
                print("   This means the server is not reachable or the port is closed.")
                return False
        except Exception as e:
            print(f"❌ Network test failed: {e}")
            return False
        
        # Test SQLAlchemy connection
        print(f"\n🗄️ Testing SQLAlchemy connection...")
        from sqlalchemy import create_engine, text
        
        engine = create_engine(DB_URL, echo=False)
        
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            test_value = result.fetchone()[0]
            
            if test_value == 1:
                print("✅ Database connection: SUCCESS")
                
                # Get database info
                version_result = connection.execute(text("SELECT VERSION() as version"))
                version = version_result.fetchone()[0]
                print(f"   MySQL Version: {version}")
                
                # Check if database exists
                db_result = connection.execute(text("SELECT DATABASE() as db_name"))
                db_name = db_result.fetchone()[0]
                print(f"   Current Database: {db_name}")
                
                return True
            else:
                print("❌ Database connection: FAILED")
                return False
                
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = test_db_connection()
    if success:
        print("\n🎉 Database connection test completed successfully!")
    else:
        print("\n❌ Database connection test failed!")
        print("\nPossible solutions:")
        print("1. Check if MySQL server is running")
        print("2. Verify the IP address and port are correct")
        print("3. Check firewall settings")
        print("4. Ensure the database credentials are correct")
        print("5. Verify network connectivity to the server") 