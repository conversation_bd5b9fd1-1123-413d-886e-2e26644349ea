#!/usr/bin/env python3
"""
Google Cloud SQL Configuration Script for Meeting Intelligence Agent

This script helps configure and troubleshoot Google Cloud SQL connections
for the meeting intelligence agent application.
"""

import os
import sys
import subprocess
import json
import socket
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import time

class GoogleCloudSQLConfigurator:
    """Configure and troubleshoot Google Cloud SQL connections."""
    
    def __init__(self):
        self.project_id = "elevation-agent-dev"
        self.instance_name = None
        self.region = "us-central1"  # Default region
        self.connection_name = None
        self.current_ip = self._get_current_ip()
        
    def _get_current_ip(self) -> str:
        """Get current public IP address."""
        try:
            response = requests.get('https://api.ipify.org', timeout=5)
            return response.text
        except:
            return "unknown"
    
    def check_gcloud_installation(self) -> bool:
        """Check if gcloud CLI is installed and configured."""
        print("🔍 Checking Google Cloud CLI installation...")
        
        try:
            # Check if gcloud is installed
            result = subprocess.run(['gcloud', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ Google Cloud CLI is installed")
                print(f"   Version: {result.stdout.split()[0]}")
                
                # Check if authenticated
                auth_result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                                           capture_output=True, text=True, timeout=10)
                if 'ACTIVE' in auth_result.stdout:
                    print("✅ Google Cloud authentication is active")
                    return True
                else:
                    print("❌ Google Cloud authentication required")
                    return False
            else:
                print("❌ Google Cloud CLI not found")
                return False
                
        except FileNotFoundError:
            print("❌ Google Cloud CLI not installed")
            return False
        except Exception as e:
            print(f"❌ Error checking gcloud: {e}")
            return False
    
    def list_sql_instances(self) -> List[Dict]:
        """List all SQL instances in the project."""
        print(f"\n🗄️ Listing SQL instances in project: {self.project_id}")
        
        try:
            result = subprocess.run([
                'gcloud', 'sql', 'instances', 'list',
                '--project', self.project_id,
                '--format', 'json'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                instances = json.loads(result.stdout)
                if instances:
                    print(f"✅ Found {len(instances)} SQL instance(s):")
                    for instance in instances:
                        print(f"   - {instance['name']} ({instance['databaseVersion']})")
                        print(f"     Region: {instance['region']}")
                        print(f"     State: {instance['state']}")
                        if 'connectionName' in instance:
                            print(f"     Connection: {instance['connectionName']}")
                        print()
                    return instances
                else:
                    print("❌ No SQL instances found")
                    return []
            else:
                print(f"❌ Error listing instances: {result.stderr}")
                return []
                
        except Exception as e:
            print(f"❌ Error listing SQL instances: {e}")
            return []
    
    def find_instance_by_ip(self, target_ip: str) -> Optional[Dict]:
        """Find SQL instance by IP address."""
        print(f"\n🔍 Searching for instance with IP: {target_ip}")
        
        instances = self.list_sql_instances()
        for instance in instances:
            try:
                # Get instance details
                result = subprocess.run([
                    'gcloud', 'sql', 'instances', 'describe', instance['name'],
                    '--project', self.project_id,
                    '--format', 'json'
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    details = json.loads(result.stdout)
                    ip_addresses = details.get('ipAddresses', [])
                    
                    for ip_info in ip_addresses:
                        if ip_info.get('ipAddress') == target_ip:
                            print(f"✅ Found matching instance: {instance['name']}")
                            return instance
                            
            except Exception as e:
                print(f"   Error checking instance {instance['name']}: {e}")
        
        print("❌ No instance found with that IP address")
        return None
    
    def get_instance_connection_info(self, instance_name: str) -> Dict:
        """Get connection information for a SQL instance."""
        print(f"\n🔗 Getting connection info for: {instance_name}")
        
        try:
            result = subprocess.run([
                'gcloud', 'sql', 'instances', 'describe', instance_name,
                '--project', self.project_id,
                '--format', 'json'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                details = json.loads(result.stdout)
                
                connection_info = {
                    'name': instance_name,
                    'region': details.get('region'),
                    'databaseVersion': details.get('databaseVersion'),
                    'state': details.get('state'),
                    'connectionName': details.get('connectionName'),
                    'ipAddresses': details.get('ipAddresses', []),
                    'settings': details.get('settings', {})
                }
                
                print("✅ Connection information:")
                print(f"   Name: {connection_info['name']}")
                print(f"   Region: {connection_info['region']}")
                print(f"   Version: {connection_info['databaseVersion']}")
                print(f"   State: {connection_info['state']}")
                print(f"   Connection Name: {connection_info['connectionName']}")
                
                for ip_info in connection_info['ipAddresses']:
                    print(f"   IP: {ip_info.get('ipAddress')} ({ip_info.get('type', 'unknown')})")
                
                return connection_info
            else:
                print(f"❌ Error getting instance details: {result.stderr}")
                return {}
                
        except Exception as e:
            print(f"❌ Error getting connection info: {e}")
            return {}
    
    def check_authorized_networks(self, instance_name: str) -> List[str]:
        """Check authorized networks for the instance."""
        print(f"\n🌐 Checking authorized networks for: {instance_name}")
        
        try:
            result = subprocess.run([
                'gcloud', 'sql', 'instances', 'describe', instance_name,
                '--project', self.project_id,
                '--format', 'json'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                details = json.loads(result.stdout)
                settings = details.get('settings', {})
                ip_config = settings.get('ipConfiguration', {})
                authorized_networks = ip_config.get('authorizedNetworks', [])
                
                if authorized_networks:
                    print("✅ Authorized networks:")
                    for network in authorized_networks:
                        print(f"   - {network.get('value')} ({network.get('name', 'unnamed')})")
                else:
                    print("❌ No authorized networks configured")
                
                return [net.get('value') for net in authorized_networks]
            else:
                print(f"❌ Error checking authorized networks: {result.stderr}")
                return []
                
        except Exception as e:
            print(f"❌ Error checking authorized networks: {e}")
            return []
    
    def add_authorized_network(self, instance_name: str, ip_address: str, name: str = None) -> bool:
        """Add an IP address to authorized networks."""
        print(f"\n➕ Adding {ip_address} to authorized networks...")
        
        try:
            cmd = [
                'gcloud', 'sql', 'instances', 'patch', instance_name,
                '--project', self.project_id,
                '--authorized-networks', f"{ip_address}/32"
            ]
            
            if name:
                cmd.extend(['--authorized-network-name', name])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"✅ Successfully added {ip_address} to authorized networks")
                return True
            else:
                print(f"❌ Error adding authorized network: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error adding authorized network: {e}")
            return False
    
    def test_connection(self, instance_name: str, database: str, user: str, password: str) -> bool:
        """Test database connection."""
        print(f"\n🔍 Testing database connection...")
        
        # Get connection info
        connection_info = self.get_instance_connection_info(instance_name)
        if not connection_info:
            return False
        
        # Find public IP
        public_ip = None
        for ip_info in connection_info['ipAddresses']:
            if ip_info.get('type') == 'PRIMARY':
                public_ip = ip_info.get('ipAddress')
                break
        
        if not public_ip:
            print("❌ No public IP found for instance")
            return False
        
        print(f"   Testing connection to: {public_ip}:3306")
        
        # Test network connectivity
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((public_ip, 3306))
            sock.close()
            
            if result == 0:
                print("✅ Network connectivity: SUCCESS")
            else:
                print(f"❌ Network connectivity: FAILED (Error code: {result})")
                return False
        except Exception as e:
            print(f"❌ Network test failed: {e}")
            return False
        
        # Test MySQL connection
        try:
            import pymysql
            
            connection = pymysql.connect(
                host=public_ip,
                port=3306,
                user=user,
                password=password,
                database=database,
                connect_timeout=10
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                if result and result[0] == 1:
                    print("✅ Database connection: SUCCESS")
                    
                    # Get database info
                    cursor.execute("SELECT VERSION()")
                    version = cursor.fetchone()[0]
                    print(f"   MySQL Version: {version}")
                    
                    cursor.execute("SELECT DATABASE()")
                    db_name = cursor.fetchone()[0]
                    print(f"   Connected to: {db_name}")
                    
                    connection.close()
                    return True
                else:
                    print("❌ Database connection: FAILED")
                    connection.close()
                    return False
                    
        except ImportError:
            print("❌ PyMySQL not installed. Install with: pip install pymysql")
            return False
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    
    def create_database_if_not_exists(self, instance_name: str, database: str, user: str, password: str) -> bool:
        """Create database if it doesn't exist."""
        print(f"\n🗄️ Creating database '{database}' if it doesn't exist...")
        
        try:
            import pymysql
            
            # Get connection info
            connection_info = self.get_instance_connection_info(instance_name)
            public_ip = None
            for ip_info in connection_info['ipAddresses']:
                if ip_info.get('type') == 'PRIMARY':
                    public_ip = ip_info.get('ipAddress')
                    break
            
            if not public_ip:
                print("❌ No public IP found for instance")
                return False
            
            # Connect without specifying database
            connection = pymysql.connect(
                host=public_ip,
                port=3306,
                user=user,
                password=password,
                connect_timeout=10
            )
            
            with connection.cursor() as cursor:
                # Check if database exists
                cursor.execute("SHOW DATABASES LIKE %s", (database,))
                exists = cursor.fetchone()
                
                if not exists:
                    cursor.execute(f"CREATE DATABASE `{database}`")
                    print(f"✅ Database '{database}' created successfully")
                else:
                    print(f"✅ Database '{database}' already exists")
                
                connection.close()
                return True
                
        except Exception as e:
            print(f"❌ Error creating database: {e}")
            return False
    
    def generate_connection_string(self, instance_name: str, database: str, user: str, password: str) -> str:
        """Generate connection string for the application."""
        print(f"\n🔗 Generating connection string...")
        
        connection_info = self.get_instance_connection_info(instance_name)
        public_ip = None
        for ip_info in connection_info['ipAddresses']:
            if ip_info.get('type') == 'PRIMARY':
                public_ip = ip_info.get('ipAddress')
                break
        
        if not public_ip:
            print("❌ No public IP found for instance")
            return ""
        
        # URL encode the password
        from urllib.parse import quote_plus
        safe_password = quote_plus(password)
        
        connection_string = f"mysql+pymysql://{user}:{safe_password}@{public_ip}:3306/{database}"
        
        print("✅ Connection string generated:")
        print(f"   {connection_string}")
        
        return connection_string
    
    def update_environment_file(self, connection_string: str):
        """Update .env file with database configuration."""
        print(f"\n📝 Updating environment configuration...")
        
        env_file = Path(".env")
        env_content = []
        
        # Parse existing .env file
        if env_file.exists():
            with open(env_file, 'r') as f:
                env_content = f.readlines()
        
        # Update or add database configuration
        db_config = {
            'MYSQL_HOST': connection_string.split('@')[1].split(':')[0],
            'MYSQL_PORT': '3306',
            'MYSQL_USERNAME': connection_string.split('://')[1].split(':')[0],
            'MYSQL_PASSWORD': connection_string.split(':')[2].split('@')[0],
            'MYSQL_DATABASE': connection_string.split('/')[-1]
        }
        
        # Update existing lines or add new ones
        updated_keys = set()
        for i, line in enumerate(env_content):
            for key, value in db_config.items():
                if line.startswith(f"{key}="):
                    env_content[i] = f"{key}={value}\n"
                    updated_keys.add(key)
                    break
        
        # Add missing keys
        for key, value in db_config.items():
            if key not in updated_keys:
                env_content.append(f"{key}={value}\n")
        
        # Write updated .env file
        with open(env_file, 'w') as f:
            f.writelines(env_content)
        
        print(f"✅ Environment file updated: {env_file}")
    
    def run_comprehensive_setup(self):
        """Run comprehensive Google Cloud SQL setup."""
        print("🚀 Google Cloud SQL Configuration Setup")
        print("=" * 50)
        
        # Step 1: Check gcloud installation
        if not self.check_gcloud_installation():
            print("\n❌ Please install and configure Google Cloud CLI first:")
            print("   1. Install: https://cloud.google.com/sdk/docs/install")
            print("   2. Authenticate: gcloud auth login")
            print("   3. Set project: gcloud config set project elevation-agent-dev")
            return False
        
        # Step 2: Find the instance
        target_ip = "*************"
        instance = self.find_instance_by_ip(target_ip)
        
        if not instance:
            print(f"\n❌ Could not find SQL instance with IP: {target_ip}")
            print("   Please check your Google Cloud Console or provide the correct instance name.")
            return False
        
        instance_name = instance['name']
        print(f"\n✅ Found instance: {instance_name}")
        
        # Step 3: Check authorized networks
        authorized_networks = self.check_authorized_networks(instance_name)
        
        if self.current_ip not in authorized_networks:
            print(f"\n➕ Adding current IP ({self.current_ip}) to authorized networks...")
            if self.add_authorized_network(instance_name, self.current_ip, "meeting-agent-dev"):
                print("✅ IP added successfully")
                # Wait a moment for changes to propagate
                time.sleep(10)
            else:
                print("❌ Failed to add IP to authorized networks")
                return False
        else:
            print(f"✅ Current IP ({self.current_ip}) is already authorized")
        
        # Step 4: Test connection
        database = "meeting-intelligence"
        user = "root"
        password = "elevationaiAgent002"  # This should be retrieved securely
        
        if self.test_connection(instance_name, database, user, password):
            print("✅ Database connection successful!")
        else:
            print("❌ Database connection failed")
            print("\nTroubleshooting steps:")
            print("1. Check if the database exists")
            print("2. Verify username and password")
            print("3. Check if SSL is required")
            return False
        
        # Step 5: Create database if needed
        if self.create_database_if_not_exists(instance_name, database, user, password):
            print("✅ Database setup complete")
        else:
            print("❌ Database setup failed")
            return False
        
        # Step 6: Generate connection string
        connection_string = self.generate_connection_string(instance_name, database, user, password)
        
        if connection_string:
            # Step 7: Update environment file
            self.update_environment_file(connection_string)
            
            print("\n🎉 Google Cloud SQL configuration completed successfully!")
            print("\nNext steps:")
            print("1. Test the application: python test_db_connection.py")
            print("2. Run the agent: python run_agent.py")
            return True
        else:
            print("❌ Failed to generate connection string")
            return False

def main():
    """Main function."""
    configurator = GoogleCloudSQLConfigurator()
    
    try:
        success = configurator.run_comprehensive_setup()
        if success:
            print("\n✅ Setup completed successfully!")
            return 0
        else:
            print("\n❌ Setup failed!")
            return 1
    except KeyboardInterrupt:
        print("\n\n⚠️ Setup interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 