# 📄 <PERSON><PERSON><PERSON><PERSON> Agent - Supported File Formats for Transcripts

## ✅ FULLY SUPPORTED FILE FORMATS

### 📝 Text-Based Formats
| Extension | Format | Support Level | Notes |
|-----------|--------|---------------|-------|
| `.txt` | Plain Text | ✅ **Full** | Best format - fastest processing |
| `.transcript` | Transcript Files | ✅ **Full** | Dedicated transcript format |
| `.vtt` | WebVTT Subtitles | ✅ **Full** | Video transcript format |
| `.srt` | SubRip Subtitles | ✅ **Full** | Common subtitle format |
| `.json` | JSON Data | ✅ **Full** | Structured transcript data |
| `.csv` | Comma-Separated | ✅ **Full** | Tabular transcript data |
| `.tsv` | Tab-Separated | ✅ **Full** | Tab-delimited data |

### 📄 Document Formats
| Extension | Format | Support Level | Notes |
|-----------|--------|---------------|-------|
| `.docx` | Microsoft Word 2007+ | ✅ **Full** | Modern Word documents |
| `.doc` | Legacy Microsoft Word | ⚠️ **Basic** | Older Word format |
| `.rtf` | Rich Text Format | ⚠️ **Basic** | Cross-platform rich text |
| `.odt` | OpenDocument Text | ✅ **Full** | Open standard document |
| `.pdf` | Portable Document | ⚠️ **Limited** | Requires additional libraries |

### 🌐 Google Workspace
| Format | Support Level | Notes |
|--------|---------------|-------|
| Google Docs | ✅ **Full** | Exported as plain text |
| Google Sheets | ✅ **Full** | Exported as CSV |
| Google Slides | ⚠️ **Limited** | Text content only |

## 🔍 FILE DETECTION CRITERIA

The agent searches for files using these criteria:

### 1. **File Extensions**
```
.txt, .docx, .doc, .pdf, .rtf, .odt, .transcript, .vtt, .srt, .json, .csv, .tsv
```

### 2. **MIME Types**
```
- text/plain
- application/vnd.openxmlformats-officedocument.wordprocessingml.document (.docx)
- application/msword (.doc)
- application/pdf (.pdf)
- application/rtf (.rtf)
- application/vnd.oasis.opendocument.text (.odt)
- application/vnd.google-apps.document (Google Docs)
- text/csv (.csv)
- application/json (.json)
```

### 3. **Filename Keywords**
Files containing these words in the filename:
```
- "transcript"
- "meeting"
- "notes"
- "summary"
- "recording"
```

### 4. **Time-Based Matching**
- Files modified within ±5 minutes of calendar event time
- Files created in the last 30 minutes (for each check interval)

## 📊 PROCESSING CAPABILITIES

### ✅ **Full Text Extraction**
- **Plain Text (.txt)**: Direct reading
- **DOCX**: XML parsing for text content
- **Google Docs**: Export as plain text
- **ODT**: XML parsing from ZIP archive
- **JSON/CSV**: Structured data parsing
- **VTT/SRT**: Subtitle format parsing

### ⚠️ **Basic Text Extraction**
- **DOC**: Binary parsing with cleanup
- **RTF**: Control code removal
- **PDF**: Requires PyPDF2/pdfplumber (install separately)

### 🔧 **Enhanced Support (Optional Libraries)**

To improve support for certain formats, install these libraries:

```bash
# For better DOCX support
pip install python-docx

# For DOC file support
pip install python-docx2txt

# For PDF support
pip install PyPDF2
# OR
pip install pdfplumber

# For advanced RTF support
pip install striprtf
```

## 📁 **FILE ORGANIZATION EXAMPLES**

### ✅ **Good File Names**
```
✅ Team-Standup-2025-07-09-transcript.txt
✅ Client-Meeting-transcript.docx
✅ Project-Review-meeting-notes.pdf
✅ Daily-Scrum-07-09-2025.vtt
✅ Board-Meeting-transcript.json
```

### ❌ **Problematic File Names**
```
❌ document1.docx (no meeting context)
❌ untitled.txt (no identifying information)
❌ IMG_001.pdf (not clearly a transcript)
```

## 🚀 **BEST PRACTICES**

### 1. **File Format Recommendations**
1. **Best**: `.txt` - Fastest processing, no dependencies
2. **Good**: `.docx` - Rich formatting, widely supported
3. **Good**: `.vtt/.srt` - Perfect for video transcripts
4. **Okay**: `.pdf` - Requires additional setup

### 2. **File Naming Convention**
```
[Meeting-Type]-[Date]-transcript.[extension]
Examples:
- Team-Standup-2025-07-09-transcript.txt
- Client-Call-2025-07-09-transcript.docx
- Board-Meeting-2025-07-09-transcript.pdf
```

### 3. **Upload Timing**
- Upload within **30 minutes** of meeting end
- Agent checks every 30 minutes at :00 and :30
- Files uploaded at 14:25 will be found at 14:30 check

### 4. **File Size Limits**
- **Recommended**: Under 10MB for fast processing
- **Maximum**: 100MB (Google Drive API limit)
- **Large files**: May be truncated for processing

## 🔧 **TESTING FILE SUPPORT**

### Test Different Formats:
```bash
# Upload test files in different formats
# Force run to test immediately
curl -X POST http://localhost:8000/agent/langchain-agent/scheduler/force-run

# Check processing results
curl http://localhost:8000/agent/langchain-agent/status
```

### Verify File Detection:
1. Upload a test transcript file
2. Share the folder with: `<EMAIL>`
3. Create a calendar event for the same time
4. Force run the agent
5. Check for generated summary

## 📊 **CURRENT CONFIGURATION STATUS**

### ✅ **Currently Supported**
- All text-based formats (.txt, .csv, .json, .vtt, .srt)
- Microsoft Word documents (.docx, .doc)
- OpenDocument Text (.odt)
- Rich Text Format (.rtf)
- Google Docs (exported as text)

### 🔧 **Requires Additional Setup**
- **PDF files**: Install `pip install PyPDF2` or `pip install pdfplumber`
- **Enhanced DOCX**: Install `pip install python-docx`
- **Better DOC support**: Install `pip install python-docx2txt`

### 🎯 **Recommended Setup**
```bash
# Install all optional libraries for maximum format support
pip install python-docx python-docx2txt PyPDF2 striprtf
```

## 📞 **TROUBLESHOOTING**

### If files aren't being found:
1. **Check file extension** - Is it in the supported list?
2. **Check filename** - Does it contain "transcript" or meeting keywords?
3. **Check timing** - Was it uploaded within 30 minutes?
4. **Check sharing** - Is the folder shared with the service account?

### If text extraction fails:
1. **Check file format** - Try converting to .txt first
2. **Install libraries** - Add optional dependencies
3. **Check file size** - Large files may timeout
4. **Check encoding** - Ensure UTF-8 encoding for text files

## 🎉 **SUMMARY**

**The LangChain agent can process:**
- ✅ **11 file extensions**: .txt, .docx, .doc, .pdf, .rtf, .odt, .transcript, .vtt, .srt, .json, .csv
- ✅ **8 MIME types**: Including all major document formats
- ✅ **Google Workspace**: Docs, Sheets (as CSV), limited Slides
- ✅ **Smart detection**: By filename keywords and timing
- ✅ **Automatic processing**: Every 30 minutes

**For best results, use .txt or .docx files with "transcript" in the filename!** 📄
