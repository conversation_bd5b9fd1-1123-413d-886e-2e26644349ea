"""
<PERSON><PERSON><PERSON>n Agent for Post meeting Intelligence Workflow

This agent executes the exact Post meetingworkflow:
1. Identify Meeting & Transcript
2. Summarize Transcript (AI)
3. Generate JSON & HTML Summaries
4. Email Summaries to Attendees
5. Store Summaries in Google Drive
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field

# LangChain Core
from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.memory import ConversationBufferWindowMemory
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.tools import BaseTool
from langchain_google_vertexai import ChatVertexAI

# Our custom tools for the Post meetingworkflow
from src.tools.langchain_calendar_tool import CalendarTool
from src.tools.langchain_drive_tool import DriveTool
from src.tools.langchain_summarizer_tool import SummarizerTool
from src.tools.langchain_notification_tool import NotificationTool
from src.tools.langchain_file_manager_tool import FileManagerTool

# Utilities
from src.utility.google_auth import GoogleAuthenticator

# Import tool configs and app metadata
from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES, APP_NAME, APP_DESCRIPTION, APP_VERSION

logger = logging.getLogger(__name__)


@dataclass
class WorkflowStep:
    """Represents a step in the Post meetingworkflow."""
    step_number: int
    step_name: str
    description: str
    completed: bool = False
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@dataclass
class WorkflowState:
    """State management for the Post meetingworkflow."""
    session_id: str
    steps: List[WorkflowStep]
    current_step: int = 1
    meetings_processed: List[Dict[str, Any]] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

    def __post_init__(self):
        if self.meetings_processed is None:
            self.meetings_processed = []
        if self.errors is None:
            self.errors = []
        if self.start_time is None:
            self.start_time = datetime.now()


class LangChainMeetingAgent:
    """
    LangChain Agent for Post meeting Intelligence Workflow.
    
    Executes the exact workflow requested:
    1. Identify Meeting & Transcript
    2. Summarize Transcript (AI)
    3. Generate JSON & HTML Summaries
    4. Email Summaries to Attendees
    5. Store Summaries in Google Drive
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the LangChain agent."""
        self.config = config or {}
        self.google_auth = GoogleAuthenticator()

        # Initialize LangChain components
        self._initialize_llm()
        self._initialize_tools()
        self._initialize_memory()
        self._initialize_agent()

        logger.info("LangChain Meeting Agent initialized for Post meetingworkflow")
    
    def _initialize_llm(self):
        """Initialize the language model."""
        # Set up Google Cloud credentials
        credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH', './keys/google-service-account.json')
        if os.path.exists(credentials_path):
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path

        # Get project configuration
        project_id = os.getenv('GOOGLE_PROJECT_ID', 'elevation-agent-dev')
        location = os.getenv('VERTEX_AI_LOCATION', 'us-central1')

        self.llm = ChatVertexAI(
            model_name="gemini-2.0-flash-001",
            project=project_id,
            location=location,
            temperature=0.1,  # Low temperature for consistent workflow execution
            max_output_tokens=4096,
            top_p=0.8,
            top_k=40,
            verbose=True
        )
        logger.info("LangChain LLM initialized")
    
    def _initialize_tools(self):
        """Initialize the 5 tools needed for the workflow."""
        self.tools = [
            CalendarTool(auth=self.google_auth),  # Step 1: Identify Meeting
            DriveTool(auth=self.google_auth),     # Step 1: Find Transcript & Step 5: Store
            SummarizerTool(),                     # Step 2: Summarize & Step 3: Generate JSON/HTML
            NotificationTool(),                   # Step 4: Email Attendees
            FileManagerTool()                     # General file operations
        ]
        
        logger.info(f"Initialized {len(self.tools)} tools for {APP_NAME} v{APP_VERSION}")
    
    def _initialize_memory(self):
        """Initialize conversation memory."""
        self.memory = ConversationBufferWindowMemory(
            k=10,  # Remember last 10 interactions
            memory_key="chat_history",
            return_messages=True,
            output_key="output"
        )
        logger.info("Agent memory initialized")
    
    def _initialize_agent(self):
        """Initialize the LangChain agent."""
        # Create the agent prompt
        self.agent_prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        # Create the agent
        self.agent = create_tool_calling_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=self.agent_prompt
        )
        
        # Create agent executor
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            memory=self.memory,
            verbose=True,
            max_iterations=5,  # Limit iterations for focused workflow
            early_stopping_method="generate",
            handle_parsing_errors=True
        )
        
        logger.info("LangChain agent executor initialized")
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the Post meetingworkflow."""
        return """You are a Meeting Intelligence Agent that executes a specific Post meetingworkflow:


1. IDENTIFY MEETING & TRANSCRIPT
   - Use calendar_tool to find meetings from the last 30 minutes
   - Use drive_tool to locate transcript files for each meeting
   - Match transcripts to meetings based on title, time, and attendees

2. SUMMARIZE TRANSCRIPT (AI)
   - Use summarizer_tool to process each transcript with AI
   - Generate comprehensive meeting summaries with key insights
   - Extract action items, decisions, and important discussion points

3. GENERATE JSON & HTML SUMMARIES
   - Ensure summarizer_tool produces both JSON and HTML formats
   - JSON for structured data storage
   - HTML for professional email distribution

4. EMAIL SUMMARIES TO ATTENDEES
   - Use notification_tool to send HTML summaries to all meeting attendees
   - Include meeting details, key outcomes, and action items
   - Ensure professional email formatting and the format of email _template                                                                                        

5. STORE SUMMARIES IN GOOGLE DRIVE
   - Use drive_tool to organize summaries in structured folders
   - Store both JSON and HTML versions
   - Maintain organized file structure for easy retrieval

 EXECUTION PRINCIPLES:
- Execute steps in EXACT order (1→2→3→4→5)
- Complete each step before moving to the next
- Handle errors gracefully but continue with remaining meetings
- Provide clear progress updates for each step
- Ensure professional quality in all outputs

AVAILABLE TOOLS:
- calendar_tool: Access Google Calendar events
- drive_tool: Search, read, and store files in Google Drive
- summarizer_tool: Generate AI-powered summaries in JSON/HTML
- notification_tool: Send professional email notifications
- file_manager_tool: Handle file operations and cleanup

Execute this workflow systematically and report progress on each step."""

    async def execute_5_step_workflow(self, time_window_minutes: int = 30) -> Dict[str, Any]:
        """
        Execute the complete Post meeting intelligence workflow.
        
        Args:
            time_window_minutes: Look for meetings in the last X minutes
            
        Returns:
            Complete workflow results
        """
        # Initialize workflow state
        workflow_state = WorkflowState(
            session_id=f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            steps=[
                WorkflowStep(1, "Identify Meeting & Transcript", "Find meetings and their transcripts"),
                WorkflowStep(2, "Summarize Transcript (AI)", "Generate AI-powered summaries"),
                WorkflowStep(3, "Generate JSON & HTML Summaries", "Create structured outputs"),
                WorkflowStep(4, "Email Summaries to Attendees", "Send professional email notifications"),
                WorkflowStep(5, "Store Summaries in Google Drive", "Organize files in structured folders")
            ]
        )
        
        logger.info(f"🚀 Starting Post meetingworkflow: {workflow_state.session_id}")
        
        try:
            # Execute the workflow
            workflow_task = f"""
Execute the Post meeting Intelligence Workflow:

TIME WINDOW: Last {time_window_minutes} minutes
SESSION ID: {workflow_state.session_id}

STEP 1: IDENTIFY MEETING & TRANSCRIPT
- Use calendar_tool to find meetings from the last {time_window_minutes} minutes
- Use drive_tool to locate transcript files for each meeting
- Match transcripts to meetings based on title, time, and attendees

STEP 2: SUMMARIZE TRANSCRIPT (AI)
- Use summarizer_tool to process each transcript with AI
- Generate comprehensive meeting summaries with key insights
- Extract action items, decisions, and important discussion points

STEP 3: GENERATE JSON & HTML SUMMARIES
- Ensure summarizer_tool produces both JSON and HTML formats
- JSON for structured data storage
- HTML for professional email distribution

STEP 4: EMAIL SUMMARIES TO ATTENDEES
- Use notification_tool to send HTML summaries to all meeting attendees
- Include meeting details, key outcomes, and action items
- Ensure professional email formatting

STEP 5: STORE SUMMARIES IN GOOGLE DRIVE
- Use drive_tool to organize summaries in structured folders
- Store both JSON and HTML versions
- Maintain organized file structure for easy retrieval

EXECUTION REQUIREMENTS:
- Execute steps in EXACT order (1→2→3→4→5)
- Complete each step before moving to the next
- Handle errors gracefully but continue with remaining meetings
- Provide clear progress updates for each step
- Report completion status for each step

Execute this workflow systematically and report your progress.
"""
            
            # Execute the workflow
            result = await self.agent_executor.ainvoke({
                "input": workflow_task
            })
            
            workflow_state.end_time = datetime.now()
            
            # Parse results and update workflow state
            workflow_output = result.get("output", "")
            
            # Create final result
            final_result = {
                "status": "completed",
                "session_id": workflow_state.session_id,
                "workflow_steps": [
                    {
                        "step_number": step.step_number,
                        "step_name": step.step_name,
                        "description": step.description,
                        "completed": True  # Assume completion for now
                    } for step in workflow_state.steps
                ],
                "execution_time": {
                    "start": workflow_state.start_time.isoformat() if workflow_state.start_time else "",
                    "end": workflow_state.end_time.isoformat() if workflow_state.end_time else "",
                    "duration_seconds": (workflow_state.end_time - workflow_state.start_time).total_seconds() if workflow_state.end_time and workflow_state.start_time else 0
                },
                "agent_output": workflow_output,
                "meetings_processed": workflow_state.meetings_processed,
                "errors": workflow_state.errors,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"✅ Post meetingworkflow completed: {workflow_state.session_id}")
            return final_result
            
        except Exception as e:
            logger.error(f"❌ Post meetingworkflow failed: {e}")
            workflow_state.end_time = datetime.now()
            workflow_state.errors.append(str(e))
            
            return {
                "status": "failed",
                "session_id": workflow_state.session_id,
                "error": str(e),
                "errors": workflow_state.errors,
                "execution_time": {
                    "start": workflow_state.start_time.isoformat() if workflow_state.start_time else "",
                    "end": workflow_state.end_time.isoformat() if workflow_state.end_time else "",
                    "duration_seconds": (workflow_state.end_time - workflow_state.start_time).total_seconds() if workflow_state.end_time and workflow_state.start_time else 0
                },
                "timestamp": datetime.now().isoformat()
            }
    
    async def chat_with_agent(self, message: Optional[str]) -> str:
        """
        Chat with the agent for custom requests.
        
        Args:
            message: User message
            
        Returns:
            Agent response
        """
        try:
            safe_message = message or ""
            result = await self.agent_executor.ainvoke({
                "input": safe_message
            })
            return result.get("output", "No response generated")
        except Exception as e:
            logger.error(f"Chat failed: {e}")
            return f"Error: {str(e)}"
    
    async def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status and capabilities."""
        return {
            "agent_type": APP_NAME,
            "agent_version": APP_VERSION,
            "workflow_steps": [
                "1. Identify Meeting & Transcript",
                "2. Summarize Transcript (AI)",
                "3. Generate JSON & HTML Summaries",
                "4. Email Summaries to Attendees",
                "5. Store Summaries in Google Drive"
            ],
            "available_tools": [
                {
                    "name": tool.name,
                    "description": getattr(tool, 'description', 'No description')
                }
                for tool in self.tools
            ],
            "workflow_purpose": APP_DESCRIPTION,
            "time_window": "Last 30 minutes",
            "output_formats": ["JSON", "HTML", "Email"],
            "storage_location": "Google Drive",
            "status": "ready",
            "timestamp": datetime.now().isoformat()
        }


# Global agent instance
_agent_instance: Optional[LangChainMeetingAgent] = None


def get_langchain_agent(config: Optional[Dict[str, Any]] = None) -> LangChainMeetingAgent:
    """Get or create the LangChain meeting agent instance."""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = LangChainMeetingAgent(config or {})
    return _agent_instance


async def run_autonomous_meeting_workflow(task_description: str = None) -> Dict[str, Any]:
    """
    Run the Post meetingautonomous meeting workflow.
    
    Args:
        task_description: Optional custom task description
        
    Returns:
        Workflow execution results
    """
    agent = get_langchain_agent()
    
    if task_description:
        # Custom task execution
        result = await agent.chat_with_agent(task_description or "")
        return {
            "status": "completed",
            "task_description": task_description or "",
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
    else:
        # Default Post meetingworkflow
        return await agent.execute_5_step_workflow()


async def chat_with_meeting_agent(message: Optional[str]) -> str:
    """
    Chat with the meeting agent.
    
    Args:
        message: User message
        
    Returns:
        Agent response
    """
    agent = get_langchain_agent()
    safe_message = message or ""
    return await agent.chat_with_agent(safe_message)
