#!/bin/bash

# Meeting Intelligence Agent Cron Job
# Runs every 30 minutes to process meeting transcripts
# Project: elevation-agent-dev

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Set environment variables for elevation-agent-dev project
export GOOGLE_PROJECT_ID="elevation-agent-dev"
export GOOGLE_APPLICATION_CREDENTIALS="$SCRIPT_DIR/keys/google-service-account.json"
export VERTEX_AI_LOCATION="us-central1"
export GMAIL_CREDENTIALS_PATH="$SCRIPT_DIR/keys/gmail-credentials.json"
export GMAIL_TOKEN_PATH="$SCRIPT_DIR/keys/gmail-token.json"

# Set working directory
cd "$SCRIPT_DIR"

# Create logging directory if it doesn't exist
mkdir -p "$SCRIPT_DIR/logging"

# Log start
echo "$(date): Starting Meeting Intelligence Workflow" >> "$SCRIPT_DIR/logging/cron.log"

# Activate virtual environment (if using)
if [ -d "$SCRIPT_DIR/.venv" ]; then
    source "$SCRIPT_DIR/.venv/bin/activate"
    echo "$(date): Activated virtual environment" >> "$SCRIPT_DIR/logging/cron.log"
fi

# Run the workflow using the Python cron script
python3 "$SCRIPT_DIR/cron_workflow.py" >> "$SCRIPT_DIR/logging/cron.log" 2>&1

# Log completion
echo "$(date): Meeting Intelligence Workflow completed" >> "$SCRIPT_DIR/logging/cron.log" 