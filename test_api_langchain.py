#!/usr/bin/env python3
"""
Test LangChain integration through the API.
"""

import requests
import json
import time

def test_api_health():
    """Test API health endpoint."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            print("✅ API health check passed")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API health check error: {e}")
        return False

def test_api_root():
    """Test API root endpoint."""
    try:
        response = requests.get("http://localhost:8001/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API root endpoint working")
            print(f"   API Name: {data.get('name', 'N/A')}")
            print(f"   Version: {data.get('version', 'N/A')}")
            return True
        else:
            print(f"❌ API root endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API root endpoint error: {e}")
        return False

def test_workflow_trigger():
    """Test workflow trigger endpoint."""
    try:
        print("   Triggering workflow...")
        response = requests.post("http://localhost:8001/agent/trigger", timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Workflow trigger successful")
            print(f"   Status: {data.get('status', 'N/A')}")
            print(f"   Session ID: {data.get('session_id', 'N/A')}")
            
            # Check if workflow steps were completed
            steps = data.get('workflow_steps', [])
            if steps:
                print(f"   Workflow Steps: {len(steps)} completed")
                for step in steps[:3]:  # Show first 3 steps
                    step_name = step.get('step_name', 'Unknown')
                    completed = step.get('completed', False)
                    status = "✅" if completed else "❌"
                    print(f"     {status} {step_name}")
            
            return True
        else:
            print(f"❌ Workflow trigger failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Workflow trigger error: {e}")
        return False

def test_agent_status():
    """Test agent status endpoint."""
    try:
        response = requests.get("http://localhost:8001/agent/status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Agent status endpoint working")
            print(f"   Agent Status: {data.get('status', 'N/A')}")
            
            # Check for LangChain information
            if 'tools' in data:
                tools = data['tools']
                print(f"   Tools Available: {len(tools)}")
                
            if 'capabilities' in data:
                capabilities = data['capabilities']
                print(f"   Capabilities: {len(capabilities)}")
                
            return True
        else:
            print(f"❌ Agent status failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Agent status error: {e}")
        return False

def main():
    """Run API tests to verify LangChain integration."""
    print("🧪 API LangChain Integration Test")
    print("=" * 40)
    
    tests = [
        ("API Health", test_api_health),
        ("API Root", test_api_root),
        ("Agent Status", test_agent_status),
        ("Workflow Trigger", test_workflow_trigger),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 Running {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 40)
    print("📊 API TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 API LangChain integration is working!")
    else:
        print("⚠️ Some API tests failed.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
