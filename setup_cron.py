#!/usr/bin/env python3
"""
Meeting Intelligence Agent - Cron Job Setup Launcher
Simple launcher that calls the actual setup script in src/utility
"""

import os
import sys
from pathlib import Path

def main():
    """Launch the actual setup script from src/utility"""
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    
    # Path to the actual setup script
    setup_script = project_root / 'src' / 'utility' / 'setup_cron.py'
    
    if not setup_script.exists():
        print(f"Error: Setup script not found at {setup_script}")
        sys.exit(1)
    
    # Change to project root and run the setup script
    os.chdir(project_root)
    
    # Import and run the setup script
    sys.path.insert(0, str(project_root / 'src' / 'utility'))
    
    try:
        import setup_cron
        setup_cron.main()
    except Exception as e:
        print(f"Error running setup script: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 