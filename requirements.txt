# Autonomous Meeting Intelligence Agent Dependencies
# Core Technologies: FastAPI + LangChain + Google Cloud Vertex AI + MySQL + Multi-Channel Notifications

# Core Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# LangChain Framework for Autonomous Agents
langchain>=0.2.0
langchain-core>=0.2.0
langchain-google-vertexai>=1.0.0
langgraph>=0.2.0

# Database
sqlalchemy>=2.0.0
aiomysql>=0.2.0
pymysql>=1.1.0

# Google Cloud Services
google-cloud-aiplatform>=1.40.0
google-generativeai>=0.3.0
google-auth>=2.20.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0
google-api-python-client>=2.100.0

# Scheduling and Automation
schedule>=1.2.0

# Multi-Channel Notifications
slack-sdk>=3.20.0
twilio>=8.0.0

# File Processing and Utilities
chardet>=5.0.0
python-multipart>=0.0.6
python-dotenv>=1.0.0
typing-extensions>=4.5.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
sqlalchemy>=1.4
pymysql  # or psycopg2 if using PostgreSQL
python-dotenv
structlog
