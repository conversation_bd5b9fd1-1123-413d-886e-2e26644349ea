# Meeting Intelligence Agent Dependencies
# Core Technologies: FastAPI + LangChain + Google Cloud Vertex AI + MySQL + Email Notifications

# Core Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# LangChain Framework for AI Orchestration
langchain>=0.2.0
langchain-core>=0.2.0
langchain-google-vertexai>=1.0.0

# Database
sqlalchemy>=2.0.0
pymysql>=1.1.0

# Google Cloud Services
google-cloud-aiplatform>=1.40.0
google-generativeai>=0.3.0
google-auth>=2.20.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0
google-api-python-client>=2.100.0

# Scheduling and Automation
schedule>=1.2.0

# Email Notifications
sendgrid>=6.10.0

# File Processing and Utilities
python-multipart>=0.0.6
python-dotenv>=1.0.0
requests>=2.25.0
