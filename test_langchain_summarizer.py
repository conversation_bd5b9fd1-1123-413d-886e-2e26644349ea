#!/usr/bin/env python3
"""
Test the updated LangChain-based AI summarizer.
"""

import os
import sys
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def create_test_transcript():
    """Create a test transcript file for testing."""
    transcript_content = """
Meeting: Product Strategy Review
Date: July 18, 2025
Attendees: <PERSON> (Product Manager), <PERSON> (Engineering Lead), <PERSON> (Design Lead), <PERSON> (Marketing Director)

[00:00] <PERSON>: Good morning everyone. Let's dive into our Q3 product strategy review. We have three main topics to cover today.

[00:30] <PERSON>: I'll start with the technical update. We've completed the API redesign and it's performing 60% better than the previous version. The new authentication system is also ready for beta testing.

[02:00] <PERSON>: From a design perspective, we've finalized the new user interface mockups. User testing showed a 40% improvement in task completion rates. I recommend we proceed with implementation.

[03:30] <PERSON>: Marketing-wise, we're seeing strong interest from enterprise clients. I think we should prioritize the enterprise features for the next release.

[04:00] <PERSON>: That's great feedback. Let's make some decisions. First, <PERSON>, when can we start the beta testing for the authentication system?

[04:15] <PERSON>: I can have the beta environment ready by next Friday. We'll need about 50 beta testers.

[04:30] <PERSON> <PERSON>: I can coordinate with our enterprise clients to get beta testers. I'll have a list ready by Wednesday.

[05:00] <PERSON> Chen: Perfect. Mike, what's the timeline for implementing the new UI?

[05:15] <PERSON> <PERSON>: If we start next week, we can have the implementation done in 3 weeks. But I'll need two additional frontend developers.

[05:45] Alex Chen: I'll work with HR to get those resources. Sarah, can your team support the additional frontend work?

[06:00] Sarah Kim: Yes, but we'll need to push back the mobile app optimization by two weeks.

[06:15] Alex Chen: That's acceptable. Let's prioritize the web interface first. 

[07:00] Jennifer Liu: One concern - our main competitor just announced a similar feature. Should we accelerate our timeline?

[07:30] Alex Chen: Good point. Let's aim to launch two weeks earlier. Mike, is that feasible?

[07:45] Mike Rodriguez: It's tight, but if we get those extra developers by Monday, we can make it work.

[08:00] Alex Chen: Alright, here are our action items:
1. Sarah - Beta environment ready by Friday
2. Jennifer - Beta tester list by Wednesday  
3. Alex - Secure additional frontend developers by Monday
4. Mike - Start UI implementation next week with accelerated timeline

[08:30] Sarah Kim: What about the mobile app timeline? Should we communicate the delay to stakeholders?

[08:45] Alex Chen: Yes, I'll handle that communication. The web interface takes priority.

[09:00] Alex Chen: Any other questions? Great, let's reconvene next Thursday to check progress.

Meeting ended at 09:15
"""
    
    # Create test transcript file
    transcript_file = current_dir / "test_langchain_transcript.txt"
    with open(transcript_file, 'w', encoding='utf-8') as f:
        f.write(transcript_content)
    
    print(f"✅ Created test transcript: {transcript_file}")
    return transcript_file

async def test_langchain_summarizer():
    """Test the LangChain-based AI summarizer."""
    print("\n🤖 Testing LangChain AI Summarizer...")
    
    try:
        from src.services.ai_summarizer import AISummarizer, TranscriptData
        
        # Create test transcript
        transcript_file = create_test_transcript()
        
        # Read transcript content
        with open(transcript_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create TranscriptData object
        transcript_data = TranscriptData(
            file_path=transcript_file,
            content=content,
            metadata={
                "meeting_title": "Product Strategy Review",
                "date": "2025-07-18",
                "attendees": ["Alex Chen", "Sarah Kim", "Mike Rodriguez", "Jennifer Liu"]
            }
        )
        
        # Initialize AI summarizer (now with LangChain)
        print("   Initializing LangChain-based AI summarizer...")
        summarizer = AISummarizer()
        print("✅ LangChain AI Summarizer initialized")
        
        # Check which models are available
        if summarizer.langchain_llm:
            print("✅ LangChain ChatVertexAI model available")
        if summarizer.gemini_model:
            print("✅ Gemini API fallback available")
        if summarizer.vertex_model:
            print("✅ Vertex AI fallback available")
        
        # Generate summary
        print("   Generating AI summary with LangChain...")
        summary = summarizer.summarize_transcript(transcript_data)
        
        if summary and hasattr(summary, 'meeting_data'):
            print("✅ LangChain AI summarization successful!")
            
            meeting_data = summary.meeting_data
            
            # Display results
            print(f"\n📋 SUMMARY RESULTS:")
            print(f"   Title: {meeting_data.meeting_metadata.get('title', 'N/A')}")
            print(f"   Attendees: {len(meeting_data.meeting_metadata.get('attendees', []))} people")
            print(f"   Executive Summary: {meeting_data.executive_summary[:150]}...")
            
            if hasattr(meeting_data, 'outcomes') and meeting_data.outcomes:
                print(f"   Key Decisions: {len(meeting_data.outcomes)} items")
                for i, outcome in enumerate(meeting_data.outcomes[:2], 1):
                    decision = outcome.get('decision', 'N/A')
                    print(f"     {i}. {decision[:100]}...")
            
            # Check file generation
            if hasattr(meeting_data, 'html_file_path') and meeting_data.html_file_path:
                html_file = Path(meeting_data.html_file_path)
                if html_file.exists():
                    print(f"✅ HTML file generated: {html_file.name}")
                    
            if hasattr(meeting_data, 'json_file_path') and meeting_data.json_file_path:
                json_file = Path(meeting_data.json_file_path)
                if json_file.exists():
                    print(f"✅ JSON file generated: {json_file.name}")
            
            return True
        else:
            print("❌ LangChain AI summarization failed - no summary generated")
            return False
            
    except Exception as e:
        print(f"❌ LangChain AI summarization error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_langchain_vs_fallback():
    """Test LangChain vs fallback behavior."""
    print("\n🔄 Testing LangChain vs Fallback Models...")
    
    try:
        from src.services.ai_summarizer import AISummarizer
        
        # Create a simple test prompt
        test_prompt = "Summarize this meeting: Team discussed project timeline and decided to launch next month."
        
        # Initialize summarizer
        summarizer = AISummarizer()
        
        # Test LangChain call
        if summarizer.langchain_llm:
            try:
                print("   Testing LangChain ChatVertexAI...")
                response = summarizer._call_ai_model(test_prompt)
                if response:
                    print("✅ LangChain call successful")
                    print(f"   Response length: {len(response)} characters")
                else:
                    print("⚠️ LangChain call returned empty response")
            except Exception as e:
                print(f"⚠️ LangChain call failed: {e}")
        
        # Test structured call
        if summarizer.langchain_llm:
            try:
                print("   Testing LangChain structured output...")
                structured_response = summarizer._call_langchain_structured(test_prompt)
                if structured_response:
                    print("✅ LangChain structured call successful")
                    print(f"   Structured response length: {len(structured_response)} characters")
                else:
                    print("⚠️ LangChain structured call returned empty response")
            except Exception as e:
                print(f"⚠️ LangChain structured call failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ LangChain vs fallback test error: {e}")
        return False

async def main():
    """Run LangChain summarizer tests."""
    print("🧪 LangChain AI Summarizer Test")
    print("=" * 50)
    
    tests = [
        ("LangChain Summarizer", test_langchain_summarizer),
        ("LangChain vs Fallback", test_langchain_vs_fallback),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 Running {test_name}...")
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 LANGCHAIN TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All LangChain tests passed! The system is using LangChain successfully.")
    else:
        print("⚠️ Some LangChain tests failed. Check the logs above.")
    
    # Cleanup
    try:
        test_file = Path("test_langchain_transcript.txt")
        if test_file.exists():
            test_file.unlink()
            print("\n🧹 Cleaned up test files")
    except:
        pass
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
