#!/usr/bin/env python3
"""
Test the Lang<PERSON>hain integration by directly calling the workflow.
"""

import os
import sys
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

async def test_direct_workflow():
    """Test the workflow directly."""
    print("🔄 Testing Direct Workflow with LangChain...")
    
    try:
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        
        print("   Starting autonomous meeting workflow...")
        result = await run_autonomous_meeting_workflow()
        
        if result:
            print("✅ Workflow completed successfully!")
            print(f"   Status: {result.get('status', 'N/A')}")
            print(f"   Session ID: {result.get('session_id', 'N/A')}")
            
            # Check execution details
            if 'execution_time' in result:
                exec_time = result['execution_time']
                duration = exec_time.get('duration_seconds', 0)
                print(f"   Execution Time: {duration:.2f} seconds")
            
            # Check workflow steps
            if 'workflow_steps' in result:
                steps = result['workflow_steps']
                print(f"   Workflow Steps: {len(steps)} completed")
                for step in steps:
                    step_name = step.get('step_name', 'Unknown')
                    completed = step.get('completed', False)
                    status = "✅" if completed else "❌"
                    print(f"     {status} {step_name}")
            
            # Check agent output
            if 'agent_output' in result:
                output = result['agent_output']
                print(f"   Agent Output: {output[:200]}...")
            
            return True
        else:
            print("❌ Workflow returned no result")
            return False
            
    except Exception as e:
        print(f"❌ Workflow error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_summarizer_directly():
    """Test the AI summarizer directly with LangChain."""
    print("\n🤖 Testing AI Summarizer with LangChain...")
    
    try:
        from src.services.ai_summarizer import AISummarizer, TranscriptData
        
        # Create a test transcript
        test_content = """
Meeting: LangChain Integration Test
Date: July 18, 2025
Attendees: Developer, AI Assistant

[00:00] Developer: Let's test the LangChain integration for meeting summarization.

[00:30] AI Assistant: I'll process this transcript using LangChain's ChatVertexAI model.

[01:00] Developer: The system should extract key decisions and action items.

[01:30] AI Assistant: Confirmed. I'll generate a structured summary with executive summary, outcomes, and next steps.

[02:00] Developer: Perfect. This test will verify that LangChain is working properly.

Meeting ended at 02:15
"""
        
        # Create TranscriptData
        transcript_data = TranscriptData(
            file_path=Path("langchain_test_transcript.txt"),
            content=test_content,
            metadata={
                "meeting_title": "LangChain Integration Test",
                "date": "2025-07-18",
                "attendees": ["Developer", "AI Assistant"]
            }
        )
        
        # Initialize and test summarizer
        print("   Initializing LangChain AI Summarizer...")
        summarizer = AISummarizer()
        
        # Check which models are available
        if hasattr(summarizer, 'langchain_llm') and summarizer.langchain_llm:
            print("✅ LangChain ChatVertexAI model available")
        else:
            print("⚠️ LangChain ChatVertexAI model not available")
            
        # Generate summary
        print("   Generating summary with LangChain...")
        summary = summarizer.summarize_transcript(transcript_data)
        
        if summary and hasattr(summary, 'meeting_data'):
            print("✅ LangChain AI summarization successful!")
            
            meeting_data = summary.meeting_data
            print(f"   Title: {meeting_data.meeting_metadata.get('title', 'N/A')}")
            print(f"   Executive Summary: {meeting_data.executive_summary[:150]}...")
            
            if hasattr(meeting_data, 'outcomes') and meeting_data.outcomes:
                print(f"   Outcomes: {len(meeting_data.outcomes)} items")
            
            return True
        else:
            print("❌ LangChain AI summarization failed")
            return False
            
    except Exception as e:
        print(f"❌ AI Summarizer error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_langchain_model_call():
    """Test direct LangChain model call."""
    print("\n📞 Testing Direct LangChain Model Call...")
    
    try:
        from src.services.ai_summarizer import AISummarizer
        
        summarizer = AISummarizer()
        
        if not summarizer.langchain_llm:
            print("❌ LangChain model not available")
            return False
        
        # Test simple call
        test_prompt = "Please respond with 'LangChain integration successful!' to confirm the connection."
        
        print("   Making direct LangChain call...")
        response = summarizer._call_ai_model(test_prompt)
        
        if response:
            print("✅ LangChain model call successful!")
            print(f"   Response: {response[:200]}...")
            return True
        else:
            print("❌ LangChain model call returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ LangChain model call error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all direct tests."""
    print("🧪 Direct LangChain Integration Test")
    print("=" * 50)
    
    tests = [
        ("LangChain Model Call", test_langchain_model_call),
        ("AI Summarizer", test_ai_summarizer_directly),
        ("Direct Workflow", test_direct_workflow),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 Running {test_name}...")
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 DIRECT TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All LangChain integration tests passed!")
        print("🔧 The system is now using LangChain for transcript summarization!")
    else:
        print("⚠️ Some tests failed. Check the logs above.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
