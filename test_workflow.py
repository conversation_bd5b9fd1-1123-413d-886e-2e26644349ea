#!/usr/bin/env python3
"""
Comprehensive test of the Meeting Intelligence Agent workflow.
Tests each component step by step to ensure the system is working properly.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_environment_setup():
    """Test that all required environment variables are set."""
    print("🔧 Testing Environment Setup...")
    
    required_vars = [
        'GOOGLE_PROJECT_ID',
        'GOOGLE_APPLICATION_CREDENTIALS', 
        'GMAIL_CREDENTIALS_PATH',
        'VERTEX_AI_LOCATION'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            print(f"✅ {var}: {value[:50]}...")
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    # Check if credential files exist
    creds_file = Path(os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))
    gmail_file = Path(os.getenv('GMAIL_CREDENTIALS_PATH'))
    
    if not creds_file.exists():
        print(f"❌ Google credentials file not found: {creds_file}")
        return False
    
    if not gmail_file.exists():
        print(f"❌ Gmail credentials file not found: {gmail_file}")
        return False
    
    print("✅ Environment setup complete")
    return True

def test_imports():
    """Test that all required modules can be imported."""
    print("\n📦 Testing Imports...")
    
    try:
        # Core imports
        from src.utility.google_auth import GoogleAuthenticator
        from src.utility.calendar_service import GoogleCalendarService
        from src.services.ai_summarizer import AISummarizer
        from src.services.email_service import EmailService
        print("✅ Core utilities imported")
        
        # Agent imports
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        print("✅ Agent imported")
        
        # API imports
        from src.api.main import app
        print("✅ API imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_google_authentication():
    """Test Google authentication setup."""
    print("\n🔐 Testing Google Authentication...")
    
    try:
        from src.utility.google_auth import GoogleAuthenticator
        
        auth = GoogleAuthenticator()
        print("✅ GoogleAuthenticator initialized")
        
        # Test calendar service
        calendar_service = auth.get_calendar_service()
        if calendar_service:
            print("✅ Calendar service authenticated")
        else:
            print("⚠️ Calendar service authentication failed")
        
        # Test drive service
        drive_service = auth.get_drive_service()
        if drive_service:
            print("✅ Drive service authenticated")
        else:
            print("⚠️ Drive service authentication failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False

def test_database_connection():
    """Test database connectivity."""
    print("\n🗄️ Testing Database Connection...")
    
    try:
        from src.configuration.config import DB_URL
        from sqlalchemy import create_engine, text
        
        engine = create_engine(DB_URL, echo=False)
        
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            test_value = result.fetchone()[0]
            
            if test_value == 1:
                print("✅ Database connection successful")
                
                # Get database info
                version_result = connection.execute(text("SELECT VERSION() as version"))
                version = version_result.fetchone()[0]
                print(f"   MySQL Version: {version}")
                
                return True
            else:
                print("❌ Database connection failed")
                return False
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_ai_services():
    """Test AI services (Vertex AI / Gemini)."""
    print("\n🤖 Testing AI Services...")
    
    try:
        from src.services.ai_summarizer import AISummarizer
        
        summarizer = AISummarizer()
        print("✅ AI Summarizer initialized")
        
        # Test with a simple prompt
        test_text = "This is a test meeting transcript. The team discussed project updates and next steps."

        try:
            from src.services.ai_summarizer import TranscriptData
            from pathlib import Path

            # Create a proper TranscriptData object
            transcript_data = TranscriptData(
                file_path=Path("test_transcript.txt"),
                content=test_text,
                metadata={"test": True}
            )

            summary = summarizer.summarize_transcript(transcript_data)
            if summary and hasattr(summary, 'executive_summary'):
                print("✅ AI summarization working")
                print(f"   Sample output: {summary.executive_summary[:100]}...")
                return True
            else:
                print("⚠️ AI summarization returned empty result")
                return False
        except Exception as ai_error:
            print(f"⚠️ AI summarization error: {ai_error}")
            print("   This might be due to API limits or configuration")
            return False
        
    except Exception as e:
        print(f"❌ AI services error: {e}")
        return False

def test_email_service():
    """Test email service setup."""
    print("\n📧 Testing Email Service...")
    
    try:
        from src.services.email_service import EmailService
        
        email_service = EmailService()
        print("✅ Email service initialized")
        
        # Test email configuration (don't actually send)
        test_result = email_service.test_connection()
        if test_result:
            print("✅ Email service connection test passed")
        else:
            print("⚠️ Email service connection test failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Email service error: {e}")
        return False

async def test_full_workflow():
    """Test the complete workflow execution."""
    print("\n🔄 Testing Full Workflow...")
    
    try:
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        
        print("   Starting autonomous meeting workflow...")
        result = await run_autonomous_meeting_workflow()
        
        if result:
            print("✅ Workflow completed successfully")
            print(f"   Result: {result}")
            return True
        else:
            print("⚠️ Workflow completed but returned no result")
            return False
        
    except Exception as e:
        print(f"❌ Workflow error: {e}")
        return False

def test_api_server():
    """Test that the API server can start."""
    print("\n🌐 Testing API Server...")
    
    try:
        from src.api.main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code == 200:
            print("✅ API health endpoint working")
        else:
            print(f"⚠️ API health endpoint returned {response.status_code}")
        
        # Test root endpoint
        response = client.get("/")
        if response.status_code == 200:
            print("✅ API root endpoint working")
        else:
            print(f"⚠️ API root endpoint returned {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API server error: {e}")
        return False

async def main():
    """Run all tests."""
    print("🧪 Meeting Intelligence Agent - Workflow Test")
    print("=" * 60)
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Imports", test_imports),
        ("Google Authentication", test_google_authentication),
        ("Database Connection", test_database_connection),
        ("AI Services", test_ai_services),
        ("Email Service", test_email_service),
        ("API Server", test_api_server),
    ]
    
    results = {}
    
    # Run synchronous tests
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Run async workflow test
    try:
        results["Full Workflow"] = await test_full_workflow()
    except Exception as e:
        print(f"❌ Full Workflow failed with exception: {e}")
        results["Full Workflow"] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
    else:
        print("⚠️ Some tests failed. Please check the configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
