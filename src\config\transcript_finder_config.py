"""
Configuration settings for the Drive Transcript Finder.

This file contains all configurable parameters for the transcript finding system.
Modify these settings to customize the behavior for your organization.
"""

import os
from typing import List, Dict, Any

class TranscriptFinderSettings:
    """
    Centralized configuration for the Drive Transcript Finder.
    
    This class provides a single location to manage all settings for the
    transcript finding and sharing system.
    """
    
    # ======================
    # AUTHENTICATION SETTINGS
    # ======================
    
    # Path to service account credentials
    SERVICE_ACCOUNT_CREDENTIALS_PATH = os.getenv(
        'GOOGLE_APPLICATION_CREDENTIALS', 
        './keys/google-service-account.json'
    )
    
    # Service account email for granting permissions
    SERVICE_ACCOUNT_EMAIL = "<EMAIL>"
    
    # Google Cloud Project ID
    PROJECT_ID = os.getenv('GOOGLE_PROJECT_ID', 'elevation-agent-dev')
    
    # ======================
    # SEARCH CONFIGURATION
    # ======================
    
    # Keywords to search for in file names and content
    # These will be combined with keywords extracted from meeting titles
    TRANSCRIPT_KEYWORDS = [
        # Direct transcript terms
        'transcript', 'transcription', 'transcripts',
        
        # Meeting documentation terms
        'meeting notes', 'notes', 'minutes', 'meeting minutes',
        'minutes of meeting', 'mom', 'summary', 'meeting summary',
        'call notes', 'discussion', 'action items','notes by Gemini',
        
        # Recording terms
        'recording', 'audio', 'video', 'voice',
        
        # Platform-specific terms
        'zoom', 'teams', 'webex', 'hangouts', 'meet',
        'zoom recording', 'teams recording',
        
        # File type indicators
        'captions', 'subtitles', 'srt', 'vtt',
        
        # Additional context terms
        'agenda', 'followup', 'follow up', 'follow-up',
        'decisions', 'next steps', 'takeaways'
    ]
    
    # File types that might contain transcripts
    TRANSCRIPT_MIME_TYPES = [
        # Text files
        'text/plain',                           # .txt files
        'text/csv',                            # .csv files
        'application/json',                    # .json files
        
        # Document files
        'application/pdf',                      # .pdf files
        'application/vnd.google-apps.document', # Google Docs
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', # .docx
        'application/msword',                   # .doc
        'application/rtf',                      # .rtf files
        
        # Spreadsheet files (for structured meeting data)
        'application/vnd.google-apps.spreadsheet', # Google Sheets
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', # .xlsx
        'application/vnd.ms-excel',             # .xls
        
        # Subtitle/Caption files
        'text/vtt',                           # .vtt subtitle files
        'text/srt',                           # .srt subtitle files
        'application/x-subrip',               # .srt alternative
        
        # Audio files (for audio transcripts)
        'audio/mpeg',                         # .mp3 files
        'audio/wav',                          # .wav files
        'audio/ogg',                          # .ogg files
        'audio/mp4',                          # .m4a files
        
        # Video files (for video transcripts)
        'video/mp4',                          # .mp4 files
        'video/avi',                          # .avi files
        'video/quicktime',                    # .mov files
        'video/webm',                         # .webm files
        
        # Presentation files (for meeting slides with notes)
        'application/vnd.google-apps.presentation', # Google Slides
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', # .pptx
        'application/vnd.ms-powerpoint',       # .ppt
    ]
    
    # File extensions to specifically look for
    TRANSCRIPT_FILE_EXTENSIONS = [
        '.txt', '.pdf', '.docx', '.doc', '.rtf',
        '.csv', '.json', '.xlsx', '.xls',
        '.vtt', '.srt', '.mp3', '.wav', '.mp4',
        '.avi', '.mov', '.webm', '.pptx', '.ppt'
    ]
    
    # ======================
    # TIME WINDOW SETTINGS
    # ======================
    
    # Time window for file search (before and after meeting)
    SEARCH_TIME_BEFORE_HOURS = 1      # Search 1 hour before meeting
    SEARCH_TIME_AFTER_HOURS = 3       # Search 3 hours after meeting
    
    # Default lookback period for calendar events (days)
    DEFAULT_CALENDAR_LOOKBACK_DAYS = 7
    
    # Maximum lookback period allowed (days)
    MAX_CALENDAR_LOOKBACK_DAYS = 30
    
    # ======================
    # API CONFIGURATION
    # ======================
    
    # API request limits
    MAX_RESULTS_PER_SEARCH = 50       # Maximum files to return per search
    API_RETRY_ATTEMPTS = 3            # Number of retry attempts for API calls
    API_RETRY_DELAY = 1               # Initial delay between retries (seconds)
    API_TIMEOUT = 30                  # Timeout for API requests (seconds)
    
    # Rate limiting settings
    REQUESTS_PER_MINUTE = 100         # Maximum requests per minute
    CONCURRENT_REQUESTS = 5           # Maximum concurrent requests
    
    # ======================
    # SEARCH STRATEGY SETTINGS
    # ======================
    
    # Enable/disable different search strategies
    ENABLE_DIRECT_ATTACHMENTS = True       # Search calendar event attachments
    ENABLE_DRIVE_SEARCH = True            # Search attendee personal drives
    ENABLE_SHARED_WITH_ME = True          # Search service account's shared files
    
    # Search priority order (1=highest priority)
    SEARCH_STRATEGY_PRIORITY = {
        'direct_attachments': 1,
        'drive_search': 2,
        'shared_with_me': 3
    }
    
    # Maximum number of keywords to use in a single search query
    MAX_KEYWORDS_PER_QUERY = 5
    
    # Minimum keyword length for search
    MIN_KEYWORD_LENGTH = 3
    
    # ======================
    # PERMISSION SETTINGS
    # ======================
    
    # Permission role to grant to service account
    SERVICE_ACCOUNT_ROLE = 'writer'    # 'writer' = Editor, 'reader' = Viewer
    
    # Whether to skip files that already have service account access
    SKIP_EXISTING_PERMISSIONS = True
    
    # ======================
    # LOGGING CONFIGURATION
    # ======================
    
    # Logging level
    LOG_LEVEL = 'INFO'                # DEBUG, INFO, WARNING, ERROR, CRITICAL
    
    # Log file path (None = console only)
    LOG_FILE_PATH = None              # './logs/transcript_finder.log'
    
    # Whether to log detailed API responses
    LOG_API_RESPONSES = False
    
    # ======================
    # FILTERING RULES
    # ======================
    
    # Minimum file size (bytes) to consider
    MIN_FILE_SIZE_BYTES = 100
    
    # Maximum file size (bytes) to consider (None = no limit)
    MAX_FILE_SIZE_BYTES = None        # 100 * 1024 * 1024  # 100MB
    
    # File name patterns to exclude (regex patterns)
    EXCLUDE_FILE_PATTERNS = [
        r'^\..*',                     # Hidden files
        r'.*\.(tmp|temp|log|cache)$', # Temporary files
        r'.*backup.*',                # Backup files
        r'.*draft.*',                 # Draft files
    ]
    
    # File name patterns to prioritize (regex patterns)
    PRIORITY_FILE_PATTERNS = [
        r'.*transcript.*',
        r'.*meeting.*notes.*',
        r'.*recording.*',
        r'.*minutes.*',
    ]
    
    # ======================
    # NOTIFICATION SETTINGS
    # ======================
    
    # Whether to send notifications about found transcripts
    ENABLE_NOTIFICATIONS = True
    
    # Email addresses to notify about processing results
    NOTIFICATION_EMAILS = [
        # '<EMAIL>',
        # '<EMAIL>'
    ]
    
    # ======================
    # ADVANCED SETTINGS
    # ======================
    
    # Whether to use fuzzy matching for keywords
    ENABLE_FUZZY_MATCHING = False
    
    # Fuzzy matching threshold (0-100)
    FUZZY_MATCH_THRESHOLD = 80
    
    # Whether to analyze file content (not just names)
    ANALYZE_FILE_CONTENT = False
    
    # Maximum file content size to analyze (bytes)
    MAX_CONTENT_ANALYSIS_SIZE = 1024 * 1024  # 1MB
    
    # Whether to use machine learning for transcript detection
    ENABLE_ML_DETECTION = False
    
    # ======================
    # CUSTOM PATTERNS
    # ======================
    
    # Custom regex patterns for different organizations
    CUSTOM_PATTERNS = {
        'zoom_recordings': r'zoom.*recording.*\d{4}-\d{2}-\d{2}',
        'teams_recordings': r'teams.*recording.*\d{4}-\d{2}-\d{2}',
        'webex_recordings': r'webex.*recording.*\d{4}-\d{2}-\d{2}',
        'meeting_notes': r'(meeting|notes|minutes).*\d{4}-\d{2}-\d{2}',
        'transcript_files': r'transcript.*\d{4}-\d{2}-\d{2}',
    }
    
    # ======================
    # DOMAIN-SPECIFIC SETTINGS
    # ======================
    
    # Domain-specific keywords for different industries
    DOMAIN_KEYWORDS = {
        'healthcare': ['patient', 'consultation', 'medical', 'clinical'],
        'legal': ['deposition', 'hearing', 'legal', 'court'],
        'education': ['lecture', 'class', 'seminar', 'course'],
        'sales': ['demo', 'presentation', 'pitch', 'proposal'],
        'support': ['support', 'troubleshooting', 'technical', 'help'],
        'general': []  # Default domain
    }
    
    # Current domain (affects keyword selection)
    CURRENT_DOMAIN = 'general'
    
    @classmethod
    def get_all_keywords(cls) -> List[str]:
        """Get all keywords including domain-specific ones."""
        keywords = cls.TRANSCRIPT_KEYWORDS.copy()
        
        # Add domain-specific keywords
        domain_keywords = cls.DOMAIN_KEYWORDS.get(cls.CURRENT_DOMAIN, [])
        keywords.extend(domain_keywords)
        
        return list(set(keywords))  # Remove duplicates
    
    @classmethod
    def get_search_config(cls) -> Dict[str, Any]:
        """Get configuration for search operations."""
        return {
            'keywords': cls.get_all_keywords(),
            'mime_types': cls.TRANSCRIPT_MIME_TYPES,
            'time_before_hours': cls.SEARCH_TIME_BEFORE_HOURS,
            'time_after_hours': cls.SEARCH_TIME_AFTER_HOURS,
            'max_results': cls.MAX_RESULTS_PER_SEARCH,
            'max_keywords_per_query': cls.MAX_KEYWORDS_PER_QUERY,
            'min_keyword_length': cls.MIN_KEYWORD_LENGTH,
        }
    
    @classmethod
    def get_auth_config(cls) -> Dict[str, Any]:
        """Get configuration for authentication."""
        return {
            'credentials_path': cls.SERVICE_ACCOUNT_CREDENTIALS_PATH,
            'service_account_email': cls.SERVICE_ACCOUNT_EMAIL,
            'project_id': cls.PROJECT_ID,
            'service_account_role': cls.SERVICE_ACCOUNT_ROLE,
        }
    
    @classmethod
    def get_api_config(cls) -> Dict[str, Any]:
        """Get configuration for API settings."""
        return {
            'retry_attempts': cls.API_RETRY_ATTEMPTS,
            'retry_delay': cls.API_RETRY_DELAY,
            'timeout': cls.API_TIMEOUT,
            'requests_per_minute': cls.REQUESTS_PER_MINUTE,
            'concurrent_requests': cls.CONCURRENT_REQUESTS,
        }
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Validate the configuration and return any errors."""
        errors = []
        
        # Check required files exist
        if not os.path.exists(cls.SERVICE_ACCOUNT_CREDENTIALS_PATH):
            errors.append(f"Service account credentials file not found: {cls.SERVICE_ACCOUNT_CREDENTIALS_PATH}")
        
        # Check time windows are positive
        if cls.SEARCH_TIME_BEFORE_HOURS < 0:
            errors.append("SEARCH_TIME_BEFORE_HOURS must be positive")
        
        if cls.SEARCH_TIME_AFTER_HOURS < 0:
            errors.append("SEARCH_TIME_AFTER_HOURS must be positive")
        
        # Check API limits are reasonable
        if cls.MAX_RESULTS_PER_SEARCH > 1000:
            errors.append("MAX_RESULTS_PER_SEARCH should not exceed 1000")
        
        if cls.API_RETRY_ATTEMPTS < 1:
            errors.append("API_RETRY_ATTEMPTS must be at least 1")
        
        # Check domain exists
        if cls.CURRENT_DOMAIN not in cls.DOMAIN_KEYWORDS:
            errors.append(f"Invalid domain: {cls.CURRENT_DOMAIN}")
        
        return errors

# Create a default instance for easy import
default_config = TranscriptFinderSettings() 