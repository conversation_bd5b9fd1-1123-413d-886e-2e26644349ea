import logging
from pathlib import Path
from typing import Optional
from src.services.drive_upload_service import DriveUploadService
from src.utility.calendar_service import GoogleCalendarService
from src.utility.google_auth import GoogleAuthenticator

logger = logging.getLogger(__name__)

def attach_html_summary_to_event(
    event_id: str,
    html_file_path: Path,
    authenticator: GoogleAuthenticator,
    calendar_id: str = 'primary',
    folder_id: Optional[str] = None
) -> bool:
    """
    Upload the meeting HTML summary to Google Drive and attach it to the specified calendar event.
    Args:
        event_id: The Google Calendar event ID
        html_file_path: Path to the HTML summary file
        authenticator: GoogleAuthenticator instance
        calendar_id: Calendar ID (default: 'primary')
        folder_id: Optional Drive folder ID to upload into
    Returns:
        True if both upload and attachment succeed, False otherwise
    """
    try:
        logger.info(f"Uploading HTML summary {html_file_path} to Drive...")
        drive_uploader = DriveUploadService(authenticator)
        file_id_and_link = drive_uploader.upload_file(
            file_path=html_file_path,
            mime_type='text/html',
            folder_id=folder_id
        )
        if not file_id_and_link:
            logger.error("Failed to upload HTML summary to Drive.")
            return False
        file_id, web_view_link = file_id_and_link
        logger.info(f"Uploaded HTML summary to Drive with file ID: {file_id}")

        logger.info(f"Attaching HTML summary to calendar event {event_id}...")
        calendar_service = GoogleCalendarService(authenticator)
        success = calendar_service.add_attachment_to_event(
            event_id=event_id,
            file_id=file_id,
            title=html_file_path.name,
            mime_type='text/html',
            web_view_link=web_view_link,
            calendar_id=calendar_id
        )
        if success:
            logger.info(f"Successfully attached HTML summary to event {event_id}.")
        else:
            logger.error(f"Failed to attach HTML summary to event {event_id}.")
        return success
    except Exception as e:
        logger.error(f"Error in attaching HTML summary to event: {e}")
        return False 