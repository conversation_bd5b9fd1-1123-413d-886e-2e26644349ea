import logging
from pathlib import Path
from typing import Op<PERSON>, <PERSON>ple
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from src.utility.google_auth import GoogleAuthenticator

logger = logging.getLogger(__name__)

class DriveUploadService:
    def __init__(self, authenticator: GoogleAuthenticator):
        self.authenticator = authenticator
        self.drive_service = authenticator.get_drive_service()
        if not self.drive_service:
            logger.error("Failed to initialize Google Drive service for upload.")

    def upload_file(self, file_path: Path, mime_type: str, folder_id: Optional[str] = None) -> Optional[Tuple[str, str]]:
        """
        Upload a file to Google Drive and return (file_id, webViewLink).
        """
        if not self.drive_service:
            logger.error("Drive service not available for upload.")
            return None
        try:
            file_metadata = {'name': file_path.name}
            if folder_id:
                file_metadata['parents'] = [folder_id]  # type: ignore[list-item]
            media = MediaFileUpload(str(file_path), mimetype=mime_type)
            uploaded = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id, webViewLink'
            ).execute()
            file_id = uploaded.get('id')
            web_view_link = uploaded.get('webViewLink')
            logger.info(f"Uploaded file to Drive: {file_path} (ID: {file_id})")
            return file_id, web_view_link
        except Exception as e:
            logger.error(f"Failed to upload file to Drive: {e}")
            return None 