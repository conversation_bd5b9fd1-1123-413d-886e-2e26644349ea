[Unit]
Description=Meeting Intelligence Agent
After=network.target
Wants=network.target

[Service]
Type=simple
User=meetingagent
Group=meetingagent
WorkingDirectory=/path/to/meeting-intelligence-agent
ExecStart=/usr/bin/python3 /path/to/meeting-intelligence-agent/start_api.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10

# Environment variables
Environment=GOOGLE_PROJECT_ID=elevation-agent-dev
Environment=GOOGLE_APPLICATION_CREDENTIALS=/path/to/meeting-intelligence-agent/keys/google-service-account.json
Environment=VERTEX_AI_LOCATION=us-central1
Environment=GMAIL_CREDENTIALS_PATH=/path/to/meeting-intelligence-agent/keys/gmail-credentials.json
Environment=GMAIL_TOKEN_PATH=/path/to/meeting-intelligence-agent/keys/gmail-token.json

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/meeting-intelligence-agent/output
ReadWritePaths=/path/to/meeting-intelligence-agent/logging
ReadWritePaths=/var/log

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target 