import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Any
from dataclasses import dataclass
from googleapiclient.discovery import build
from google.oauth2 import service_account
from google.auth.transport.requests import Request
import re
import time
from functools import wraps

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class TranscriptFile:
    """Represents a discovered transcript file"""
    file_id: str
    name: str
    mime_type: str
    created_time: str
    modified_time: str
    owner_email: str
    discovery_method: str  # 'attachment', 'drive_search', 'shared_with_me'
    keywords_matched: List[str]
    attendee_email: Optional[str] = None  # Which attendee's drive this came from

@dataclass
class MeetingEvent:
    """Represents a calendar meeting event"""
    event_id: str
    title: str
    start_time: datetime
    end_time: datetime
    attendees: List[str]
    organizer: str
    attachments: List[Dict[str, Any]]
    description: Optional[str] = None

class TranscriptFinderConfig:
    """Configuration for transcript finding"""
    
    # Keywords to search for in file names and content
    TRANSCRIPT_KEYWORDS = [
        'transcript', 'transcription', 'notes', 'meeting notes',
        'minutes', 'summary', 'recording', 'call notes',
        'discussion', 'audio', 'video', 'zoom', 'teams',
        'minutes of meeting', 'mom', 'action items'
    ]
    
    # File types that might contain transcripts
    TRANSCRIPT_MIME_TYPES = [
        'text/plain',                           # .txt files
        'application/pdf',                      # .pdf files
        'application/vnd.google-apps.document', # Google Docs
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', # .docx
        'application/msword',                   # .doc
        'text/csv',                            # .csv files
        'application/json',                    # .json files
        'text/vtt',                           # .vtt subtitle files
        'text/srt',                           # .srt subtitle files
        'audio/mpeg',                         # .mp3 files
        'audio/wav',                          # .wav files
        'video/mp4',                          # .mp4 files
        'video/avi',                          # .avi files
        'application/vnd.google-apps.spreadsheet' # Google Sheets
    ]
    
    # Time window for file search (before and after meeting)
    SEARCH_TIME_BEFORE_HOURS = 1
    SEARCH_TIME_AFTER_HOURS = 3
    
    # API request limits
    MAX_RESULTS_PER_SEARCH = 50
    API_RETRY_ATTEMPTS = 3
    API_RETRY_DELAY = 1  # seconds

def retry_on_failure(max_attempts: int = 3, delay: float = 1.0):
    """Decorator for retrying failed API calls"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}")
                    if attempt == max_attempts - 1:
                        raise
                    time.sleep(delay * (2 ** attempt))  # Exponential backoff
            return None
        return wrapper
    return decorator

class DriveTranscriptFinder:
    """
    Comprehensive service for finding and sharing Google Drive transcript files
    using Domain-Wide Delegation (DWD) and multiple search strategies.
    """
    
    def __init__(self, credentials_path: str, config: TranscriptFinderConfig = None):
        """
        Initialize the DriveTranscriptFinder.
        
        Args:
            credentials_path: Path to service account credentials JSON file
            config: Configuration object (uses default if None)
        """
        self.credentials_path = credentials_path
        self.config = config or TranscriptFinderConfig()
        self.service_account_email = "<EMAIL>"
        
        # Load service account credentials
        self.base_credentials = service_account.Credentials.from_service_account_file(
            credentials_path,
            scopes=[
                'https://www.googleapis.com/auth/calendar.readonly',
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/drive.file'
            ]
        )
        
        logger.info(f"Initialized DriveTranscriptFinder with service account: {self.service_account_email}")
    
    def get_impersonated_service(self, service_name: str, version: str, subject_email: str):
        """
        Get a Google API service with impersonated credentials.
        
        Args:
            service_name: Name of the Google service ('calendar', 'drive')
            version: API version ('v3', 'v1')
            subject_email: Email of the user to impersonate
            
        Returns:
            Google API service object
        """
        try:
            # Create delegated credentials for the specified user
            delegated_credentials = self.base_credentials.with_subject(subject_email)
            
            # Build and return the service
            service = build(service_name, version, credentials=delegated_credentials)
            logger.debug(f"Created {service_name} service impersonating {subject_email}")
            return service
            
        except Exception as e:
            logger.error(f"Failed to create {service_name} service for {subject_email}: {str(e)}")
            raise
    
    def extract_keywords_from_title(self, title: str) -> List[str]:
        """
        Extract relevant keywords from meeting title for file search.
        
        Args:
            title: Meeting title
            
        Returns:
            List of keywords to search for
        """
        keywords = []
        
        # Clean title - remove common meeting prefixes/suffixes
        clean_title = re.sub(r'(weekly|daily|monthly|standup|sync|meeting|call)', '', title.lower())
        clean_title = re.sub(r'[^\w\s]', ' ', clean_title)
        
        # Extract meaningful words (longer than 2 characters)
        words = [word.strip() for word in clean_title.split() if len(word.strip()) > 2]
        keywords.extend(words)
        
        # Add base transcript keywords
        keywords.extend(self.config.TRANSCRIPT_KEYWORDS)
        
        # Remove duplicates and return
        return list(set(keywords))
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    def fetch_calendar_events(self, user_email: str, days_back: int = 7) -> List[MeetingEvent]:
        """
        Fetch calendar events for a user within the specified time window.
        
        Args:
            user_email: Email of the user whose calendar to read
            days_back: Number of days to look back
            
        Returns:
            List of MeetingEvent objects
        """
        try:
            # Get calendar service impersonating the user
            calendar_service = self.get_impersonated_service('calendar', 'v3', user_email)
            
            # Calculate time range
            now = datetime.utcnow()
            time_min = (now - timedelta(days=days_back)).isoformat() + 'Z'
            time_max = now.isoformat() + 'Z'
            
            logger.info(f"Fetching calendar events for {user_email} from {time_min} to {time_max}")
            
            # Fetch events
            events_result = calendar_service.events().list(
                calendarId='primary',
                timeMin=time_min,
                timeMax=time_max,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            events = events_result.get('items', [])
            logger.info(f"Found {len(events)} calendar events for {user_email}")
            
            meeting_events = []
            for event in events:
                # Skip events without attendees (solo events)
                if 'attendees' not in event:
                    continue
                
                # Extract event details
                title = event.get('summary', 'No Title')
                start_time = self._parse_datetime(event['start'])
                end_time = self._parse_datetime(event['end'])
                
                # Extract attendees
                attendees = []
                for attendee in event.get('attendees', []):
                    email = attendee.get('email')
                    if email:
                        attendees.append(email)
                
                # Extract organizer
                organizer = event.get('organizer', {}).get('email', user_email)
                
                # Extract attachments
                attachments = event.get('attachments', [])
                
                # Extract description
                description = event.get('description', '')
                
                meeting_event = MeetingEvent(
                    event_id=event['id'],
                    title=title,
                    start_time=start_time,
                    end_time=end_time,
                    attendees=attendees,
                    organizer=organizer,
                    attachments=attachments,
                    description=description
                )
                
                meeting_events.append(meeting_event)
                logger.debug(f"Processed event: {title} with {len(attendees)} attendees")
            
            return meeting_events
            
        except Exception as e:
            logger.error(f"Failed to fetch calendar events for {user_email}: {str(e)}")
            raise
    
    def _parse_datetime(self, dt_dict: Dict[str, Any]) -> datetime:
        """Parse datetime from Google Calendar event"""
        if 'dateTime' in dt_dict:
            # Parse full datetime
            dt_str = dt_dict['dateTime']
            # Remove timezone info for simplicity
            dt_str = dt_str.split('+')[0].split('-')[0].rstrip('Z')
            return datetime.fromisoformat(dt_str.replace('Z', ''))
        elif 'date' in dt_dict:
            # All-day event
            return datetime.fromisoformat(dt_dict['date'])
        else:
            return datetime.utcnow()
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    def search_direct_attachments(self, event: MeetingEvent, impersonated_user: str) -> List[TranscriptFile]:
        """
        Search for transcript files in direct event attachments.
        
        Args:
            event: MeetingEvent object
            impersonated_user: Email of user to impersonate for Drive access
            
        Returns:
            List of TranscriptFile objects
        """
        transcript_files = []
        
        if not event.attachments:
            logger.debug(f"No attachments found for event: {event.title}")
            return transcript_files
        
        try:
            # Get Drive service impersonating the user
            drive_service = self.get_impersonated_service('drive', 'v3', impersonated_user)
            
            logger.info(f"Searching {len(event.attachments)} direct attachments for event: {event.title}")
            
            for attachment in event.attachments:
                try:
                    file_id = attachment.get('fileId')
                    if not file_id:
                        continue
                    
                    # Get file details
                    file_info = drive_service.files().get(
                        fileId=file_id,
                        fields='id,name,mimeType,createdTime,modifiedTime,owners'
                    ).execute()
                    
                    # Check if this could be a transcript file
                    keywords = self.extract_keywords_from_title(event.title)
                    if self._is_potential_transcript(file_info, keywords):
                        owner_email = file_info.get('owners', [{}])[0].get('emailAddress', 'unknown')
                        
                        transcript_file = TranscriptFile(
                            file_id=file_id,
                            name=file_info['name'],
                            mime_type=file_info['mimeType'],
                            created_time=file_info['createdTime'],
                            modified_time=file_info['modifiedTime'],
                            owner_email=owner_email,
                            discovery_method='attachment',
                            keywords_matched=self._find_matching_keywords(file_info['name'], keywords),
                            attendee_email=impersonated_user
                        )
                        
                        transcript_files.append(transcript_file)
                        logger.info(f"Found transcript attachment: {file_info['name']}")
                
                except Exception as e:
                    logger.warning(f"Failed to process attachment {attachment}: {str(e)}")
                    continue
            
            logger.info(f"Found {len(transcript_files)} transcript files in direct attachments")
            return transcript_files
            
        except Exception as e:
            logger.error(f"Failed to search direct attachments for {event.title}: {str(e)}")
            return transcript_files
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    def search_user_drive(self, event: MeetingEvent, user_email: str) -> List[TranscriptFile]:
        """
        Search a user's Drive for transcript files related to the meeting.
        
        Args:
            event: MeetingEvent object
            user_email: Email of the user whose Drive to search
            
        Returns:
            List of TranscriptFile objects
        """
        transcript_files = []
        
        try:
            # Get Drive service impersonating the user
            drive_service = self.get_impersonated_service('drive', 'v3', user_email)
            
            # Extract keywords from meeting title
            keywords = self.extract_keywords_from_title(event.title)
            
            # Calculate time window for search
            time_window_start = event.start_time - timedelta(hours=self.config.SEARCH_TIME_BEFORE_HOURS)
            time_window_end = event.end_time + timedelta(hours=self.config.SEARCH_TIME_AFTER_HOURS)
            
            logger.info(f"Searching {user_email}'s Drive for files related to: {event.title}")
            logger.debug(f"Search time window: {time_window_start} to {time_window_end}")
            
            # Build search query
            query_parts = []
            
            # Add keyword searches
            keyword_queries = []
            for keyword in keywords[:5]:  # Limit to avoid overly complex queries
                keyword_queries.append(f"name contains '{keyword}'")
            
            if keyword_queries:
                query_parts.append(f"({' or '.join(keyword_queries)})")
            
            # Add MIME type filter
            mime_type_queries = []
            for mime_type in self.config.TRANSCRIPT_MIME_TYPES:
                mime_type_queries.append(f"mimeType = '{mime_type}'")
            
            if mime_type_queries:
                query_parts.append(f"({' or '.join(mime_type_queries)})")
            
            # Add time range filter
            start_time_str = time_window_start.isoformat() + 'Z'
            end_time_str = time_window_end.isoformat() + 'Z'
            query_parts.append(f"modifiedTime >= '{start_time_str}'")
            query_parts.append(f"modifiedTime <= '{end_time_str}'")
            
            # Combine query parts
            full_query = ' and '.join(query_parts)
            logger.debug(f"Drive search query: {full_query}")
            
            # Execute search
            results = drive_service.files().list(
                q=full_query,
                fields='files(id,name,mimeType,createdTime,modifiedTime,owners)',
                pageSize=self.config.MAX_RESULTS_PER_SEARCH
            ).execute()
            
            files = results.get('files', [])
            logger.info(f"Found {len(files)} potential transcript files in {user_email}'s Drive")
            
            # Process results
            for file_info in files:
                if self._is_potential_transcript(file_info, keywords):
                    owner_email = file_info.get('owners', [{}])[0].get('emailAddress', user_email)
                    
                    transcript_file = TranscriptFile(
                        file_id=file_info['id'],
                        name=file_info['name'],
                        mime_type=file_info['mimeType'],
                        created_time=file_info['createdTime'],
                        modified_time=file_info['modifiedTime'],
                        owner_email=owner_email,
                        discovery_method='drive_search',
                        keywords_matched=self._find_matching_keywords(file_info['name'], keywords),
                        attendee_email=user_email
                    )
                    
                    transcript_files.append(transcript_file)
                    logger.info(f"Found transcript file: {file_info['name']} in {user_email}'s Drive")
            
            return transcript_files
            
        except Exception as e:
            logger.error(f"Failed to search Drive for {user_email}: {str(e)}")
            return transcript_files
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    def search_shared_with_me(self, event: MeetingEvent) -> List[TranscriptFile]:
        """
        Search service account's "Shared with me" for transcript files.
        
        Args:
            event: MeetingEvent object
            
        Returns:
            List of TranscriptFile objects
        """
        transcript_files = []
        
        try:
            # Use base service account credentials (no impersonation)
            drive_service = build('drive', 'v3', credentials=self.base_credentials)
            
            # Extract keywords from meeting title
            keywords = self.extract_keywords_from_title(event.title)
            
            # Calculate time window for search
            time_window_start = event.start_time - timedelta(hours=self.config.SEARCH_TIME_BEFORE_HOURS)
            time_window_end = event.end_time + timedelta(hours=self.config.SEARCH_TIME_AFTER_HOURS)
            
            logger.info(f"Searching service account's 'Shared with me' for files related to: {event.title}")
            
            # Build search query for shared files
            query_parts = []
            
            # Add keyword searches
            keyword_queries = []
            for keyword in keywords[:5]:  # Limit to avoid overly complex queries
                keyword_queries.append(f"name contains '{keyword}'")
            
            if keyword_queries:
                query_parts.append(f"({' or '.join(keyword_queries)})")
            
            # Add MIME type filter
            mime_type_queries = []
            for mime_type in self.config.TRANSCRIPT_MIME_TYPES:
                mime_type_queries.append(f"mimeType = '{mime_type}'")
            
            if mime_type_queries:
                query_parts.append(f"({' or '.join(mime_type_queries)})")
            
            # Add time range filter
            start_time_str = time_window_start.isoformat() + 'Z'
            end_time_str = time_window_end.isoformat() + 'Z'
            query_parts.append(f"modifiedTime >= '{start_time_str}'")
            query_parts.append(f"modifiedTime <= '{end_time_str}'")
            
            # Add shared with me filter
            query_parts.append("sharedWithMe = true")
            
            # Combine query parts
            full_query = ' and '.join(query_parts)
            logger.debug(f"Shared with me search query: {full_query}")
            
            # Execute search
            results = drive_service.files().list(
                q=full_query,
                fields='files(id,name,mimeType,createdTime,modifiedTime,owners,sharingUser)',
                pageSize=self.config.MAX_RESULTS_PER_SEARCH
            ).execute()
            
            files = results.get('files', [])
            logger.info(f"Found {len(files)} potential transcript files in 'Shared with me'")
            
            # Process results - only include files shared by meeting attendees
            attendee_emails = set(event.attendees + [event.organizer])
            
            for file_info in files:
                # Check if file was shared by a meeting attendee
                sharing_user = file_info.get('sharingUser', {})
                sharing_email = sharing_user.get('emailAddress', '')
                
                if sharing_email in attendee_emails and self._is_potential_transcript(file_info, keywords):
                    owner_email = file_info.get('owners', [{}])[0].get('emailAddress', sharing_email)
                    
                    transcript_file = TranscriptFile(
                        file_id=file_info['id'],
                        name=file_info['name'],
                        mime_type=file_info['mimeType'],
                        created_time=file_info['createdTime'],
                        modified_time=file_info['modifiedTime'],
                        owner_email=owner_email,
                        discovery_method='shared_with_me',
                        keywords_matched=self._find_matching_keywords(file_info['name'], keywords),
                        attendee_email=sharing_email
                    )
                    
                    transcript_files.append(transcript_file)
                    logger.info(f"Found transcript file shared by attendee: {file_info['name']}")
            
            return transcript_files
            
        except Exception as e:
            logger.error(f"Failed to search 'Shared with me' for {event.title}: {str(e)}")
            return transcript_files
    
    def _is_potential_transcript(self, file_info: Dict[str, Any], keywords: List[str]) -> bool:
        """
        Determine if a file is potentially a transcript based on name and type.
        
        Args:
            file_info: File information from Drive API
            keywords: Keywords to match against
            
        Returns:
            True if file is potentially a transcript
        """
        file_name = file_info.get('name', '').lower()
        mime_type = file_info.get('mimeType', '')
        
        # Check MIME type
        if mime_type not in self.config.TRANSCRIPT_MIME_TYPES:
            return False
        
        # Check for keyword matches
        for keyword in keywords:
            if keyword.lower() in file_name:
                return True
        
        return False
    
    def _find_matching_keywords(self, file_name: str, keywords: List[str]) -> List[str]:
        """
        Find which keywords match in the file name.
        
        Args:
            file_name: Name of the file
            keywords: Keywords to check against
            
        Returns:
            List of matching keywords
        """
        matching_keywords = []
        file_name_lower = file_name.lower()
        
        for keyword in keywords:
            if keyword.lower() in file_name_lower:
                matching_keywords.append(keyword)
        
        return matching_keywords
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    def grant_service_account_access(self, file_id: str, impersonated_user: str) -> bool:
        """
        Grant Editor permission to the service account for a specific file.
        
        Args:
            file_id: ID of the file to grant access to
            impersonated_user: Email of user to impersonate for permission granting
            
        Returns:
            True if permission was granted successfully
        """
        try:
            # Get Drive service impersonating the user
            drive_service = self.get_impersonated_service('drive', 'v3', impersonated_user)
            
            # Check if service account already has access
            try:
                permissions = drive_service.permissions().list(fileId=file_id).execute()
                for permission in permissions.get('permissions', []):
                    if permission.get('emailAddress') == self.service_account_email:
                        logger.info(f"Service account already has access to file: {file_id}")
                        return True
            except Exception as e:
                logger.debug(f"Could not check existing permissions: {str(e)}")
            
            # Grant Editor permission to service account
            permission = {
                'type': 'user',
                'role': 'writer',  # Editor permission
                'emailAddress': self.service_account_email
            }
            
            drive_service.permissions().create(
                fileId=file_id,
                body=permission,
                fields='id'
            ).execute()
            
            logger.info(f"Granted Editor access to service account for file: {file_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to grant access to file {file_id}: {str(e)}")
            return False
    
    def find_and_share_transcripts(self, user_email: str, days_back: int = 7) -> Dict[str, Any]:
        """
        Main method to find and share transcript files for a user's calendar events.
        
        Args:
            user_email: Email of the user whose calendar to process
            days_back: Number of days to look back for events
            
        Returns:
            Dictionary with processing results and statistics
        """
        results = {
            'processed_events': 0,
            'total_files_found': 0,
            'unique_files_found': 0,
            'files_successfully_shared': 0,
            'events_processed': [],
            'errors': []
        }
        
        try:
            logger.info(f"Starting transcript finding process for {user_email}")
            
            # Fetch calendar events
            events = self.fetch_calendar_events(user_email, days_back)
            results['processed_events'] = len(events)
            
            # Track unique files across all events
            all_transcript_files = {}  # file_id -> TranscriptFile
            
            # Process each event
            for event in events:
                event_result = {
                    'event_id': event.event_id,
                    'title': event.title,
                    'start_time': event.start_time.isoformat(),
                    'attendees': event.attendees,
                    'files_found': [],
                    'errors': []
                }
                
                logger.info(f"Processing event: {event.title}")
                
                # Strategy 1: Search direct attachments
                try:
                    attachment_files = self.search_direct_attachments(event, user_email)
                    for file in attachment_files:
                        all_transcript_files[file.file_id] = file
                    logger.info(f"Found {len(attachment_files)} files in direct attachments")
                except Exception as e:
                    error_msg = f"Failed to search direct attachments: {str(e)}"
                    event_result['errors'].append(error_msg)
                    logger.error(error_msg)
                
                # Strategy 2: Search each attendee's Drive
                for attendee_email in event.attendees:
                    try:
                        attendee_files = self.search_user_drive(event, attendee_email)
                        for file in attendee_files:
                            all_transcript_files[file.file_id] = file
                        logger.info(f"Found {len(attendee_files)} files in {attendee_email}'s Drive")
                    except Exception as e:
                        error_msg = f"Failed to search {attendee_email}'s Drive: {str(e)}"
                        event_result['errors'].append(error_msg)
                        logger.error(error_msg)
                
                # Strategy 3: Search service account's "Shared with me"
                try:
                    shared_files = self.search_shared_with_me(event)
                    for file in shared_files:
                        all_transcript_files[file.file_id] = file
                    logger.info(f"Found {len(shared_files)} files in 'Shared with me'")
                except Exception as e:
                    error_msg = f"Failed to search 'Shared with me': {str(e)}"
                    event_result['errors'].append(error_msg)
                    logger.error(error_msg)
                
                # Record files found for this event
                event_files = [f for f in all_transcript_files.values() 
                              if f.file_id not in [ef['file_id'] for ef in event_result['files_found']]]
                event_result['files_found'] = [
                    {
                        'file_id': f.file_id,
                        'name': f.name,
                        'discovery_method': f.discovery_method,
                        'keywords_matched': f.keywords_matched
                    } for f in event_files
                ]
                
                results['events_processed'].append(event_result)
                logger.info(f"Completed processing event: {event.title}")
            
            # Update statistics
            results['total_files_found'] = sum(len(event['files_found']) for event in results['events_processed'])
            results['unique_files_found'] = len(all_transcript_files)
            
            # Grant access to service account for all unique files
            logger.info(f"Granting access to {len(all_transcript_files)} unique transcript files")
            
            for file_id, transcript_file in all_transcript_files.items():
                try:
                    # Use the attendee who owns/shared the file for impersonation
                    impersonation_user = transcript_file.attendee_email or transcript_file.owner_email
                    
                    success = self.grant_service_account_access(file_id, impersonation_user)
                    if success:
                        results['files_successfully_shared'] += 1
                        logger.info(f"Successfully granted access to: {transcript_file.name}")
                    else:
                        results['errors'].append(f"Failed to grant access to: {transcript_file.name}")
                        
                except Exception as e:
                    error_msg = f"Failed to grant access to {transcript_file.name}: {str(e)}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            logger.info(f"Transcript finding process completed successfully")
            logger.info(f"Summary: {results['processed_events']} events, {results['unique_files_found']} unique files, {results['files_successfully_shared']} successfully shared")
            
            return results
            
        except Exception as e:
            error_msg = f"Critical error in transcript finding process: {str(e)}"
            results['errors'].append(error_msg)
            logger.error(error_msg)
            return results

# Example usage and configuration
def main():
    """
    Example usage of the DriveTranscriptFinder
    """
    # Configuration
    credentials_path = "./keys/google-service-account.json"
    user_email = "<EMAIL>"  # Replace with actual user email
    days_back = 7
    
    # Initialize finder
    finder = DriveTranscriptFinder(credentials_path)
    
    # Process transcripts
    results = finder.find_and_share_transcripts(user_email, days_back)
    
    # Print results
    print(f"\n=== TRANSCRIPT FINDING RESULTS ===")
    print(f"Events processed: {results['processed_events']}")
    print(f"Total files found: {results['total_files_found']}")
    print(f"Unique files found: {results['unique_files_found']}")
    print(f"Files successfully shared: {results['files_successfully_shared']}")
    
    if results['errors']:
        print(f"\nErrors encountered:")
        for error in results['errors']:
            print(f"- {error}")
    
    # Detailed event breakdown
    print(f"\n=== EVENT BREAKDOWN ===")
    for event in results['events_processed']:
        print(f"\nEvent: {event['title']}")
        print(f"Files found: {len(event['files_found'])}")
        for file in event['files_found']:
            print(f"  - {file['name']} (via {file['discovery_method']})")

if __name__ == "__main__":
    main() 