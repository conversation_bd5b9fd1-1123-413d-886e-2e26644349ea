# 📧 <PERSON><PERSON><PERSON>n Agent - Transcript Access Configuration

## 🔑 SERVICE ACCOUNT EMAIL
```
<EMAIL>
```

## 📧 PRIMARY GMAIL ACCOUNT
```
<EMAIL>
```

## 📅 CALENDAR ACCESS

### How It Works:
1. Agent uses service account to access Google Calendar
2. Checks calendars shared with: `<EMAIL>`
3. Looks for events in 30-minute intervals (every :00 and :30)

### To Share Your Calendar:
1. Open Google Calendar
2. Settings → Share with specific people
3. Add: `<EMAIL>`
4. Permission: "See all event details"

## 📁 TRANSCRIPT SEARCH

### Where Agent Looks:
- **Google Drive folders** shared with service account
- **File types:** .txt, .transcript, any text files
- **Search criteria:** Files containing "transcript" in name
- **Time window:** Last 30 minutes from calendar events

### File Matching Logic:
1. **Time-based:** Files modified ±5 minutes from meeting time
2. **Name-based:** Files containing meeting title keywords
3. **Type-based:** Text files (.txt, .transcript, etc.)

### To Share Drive Folder:
1. Open Google Drive
2. Right-click transcript folder
3. Share → Add: `<EMAIL>`
4. Permission: "Editor"

## 🔍 SEARCH PROCESS

### Every 30 Minutes:
1. **06:00** - Check calendar events from 05:30-06:00
2. **06:30** - Check calendar events from 06:00-06:30
3. **07:00** - Check calendar events from 06:30-07:00
4. And so on...

### For Each Event Found:
1. Search Drive for matching transcript files
2. Look for files modified around meeting time (±5 minutes)
3. Match by meeting title, attendees, or meeting ID
4. Process only new/unprocessed transcripts

## 📊 CURRENT CONFIGURATION

### Email Accounts:
- **Service Account:** <EMAIL>
- **Gmail Account:** <EMAIL>
- **Project:** elevation-agent-dev

### Access Requirements:
- ✅ Service account has Google Calendar API access
- ✅ Service account has Google Drive API access
- ⚠️ Calendars must be shared with service account
- ⚠️ Drive folders must be shared with service account

### File Organization:
- **Input:** Any shared Drive folder with transcript files
- **Output:** `/Meeting_Summaries_HTML/YYYY/MM/Project-Name/`
- **Formats:** HTML summaries, JSON data, email notifications

## 🚀 SETUP CHECKLIST

### For Calendar Access:
- [ ] Share your calendar with: <EMAIL>
- [ ] Grant "See all event details" permission
- [ ] Test: Agent should find your calendar events

### For Transcript Access:
- [ ] Share transcript folder with: <EMAIL>
- [ ] Grant "Editor" permission
- [ ] Save transcripts as .txt files or with "transcript" in filename
- [ ] Upload transcripts within 30 minutes of meeting end

### For Testing:
- [ ] Schedule a test meeting
- [ ] Upload a test transcript file
- [ ] Run: `curl -X POST http://localhost:8000/agent/langchain-agent/scheduler/force-run`
- [ ] Check for generated summary in Drive

## 🔧 TROUBLESHOOTING

### If Agent Can't Find Transcripts:
1. **Check sharing:** Is Drive folder shared with service account?
2. **Check timing:** Are files uploaded within 30 minutes?
3. **Check naming:** Do files contain "transcript" or are .txt files?
4. **Check calendar:** Is calendar shared with service account?

### If Agent Can't Access Calendar:
1. **Check sharing:** Is calendar shared with service account?
2. **Check permissions:** Does service account have "See all event details"?
3. **Check API:** Are Calendar APIs enabled in Google Cloud Console?

### Test Commands:
```bash
# Check agent status
python quick_status.py

# Force immediate run
curl -X POST http://localhost:8000/agent/langchain-agent/scheduler/force-run

# Check detailed status
python check_agent_status.py
```

## 📞 SUPPORT

If you need help configuring access:
1. Check the service account email is correct
2. Verify calendar and drive sharing permissions
3. Test with a simple meeting and transcript file
4. Use force-run to test immediately without waiting

The agent will automatically find and process transcripts from any calendar events and drive files shared with the service account email.
