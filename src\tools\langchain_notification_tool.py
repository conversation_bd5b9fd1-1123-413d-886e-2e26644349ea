"""LangChain Notification Tool for Post meeting intelligence workflow."""

import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun

from src.services.notification_service import NotificationService, NotificationChannel, NotificationPriority, NotificationRequest
from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES

logger = logging.getLogger(__name__)


class NotificationTool(BaseTool):
    """
    LangChain tool for sending meeting notifications (email, Slack, etc.).
    
    This tool allows the agent to:
    - Send email summaries to meeting attendees
    - Send admin alerts for errors or important events
    - Customize email content for professional distribution
    """
    
    name: str = "notification_tool"
    description: str = AVAILABLE_TOOLS["email_tool"]["description"]
    category: str = AVAILABLE_TOOLS["email_tool"]["category"]

    # Declare service as class variable to avoid Pydantic validation issues
    notification_service: Optional[NotificationService] = None

    def __init__(self):
        super().__init__()
        try:
            self.notification_service = NotificationService()
            logger.info("NotificationTool initialized with email service")
        except Exception as e:
            logger.error(f"Failed to initialize NotificationTool: {e}")
            self.notification_service = None
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute notification operations."""
        try:
            if "send meeting summary" in query.lower():
                return self._send_meeting_summary(query)
            elif "send admin alert" in query.lower():
                return self._send_admin_alert(query)
            elif "email summary" in query.lower():
                return self._send_meeting_summary(query)
            else:
                return self._send_meeting_summary(query)
                
        except Exception as e:
            logger.error(f"Notification tool error: {e}")
            return f"Error sending notification: {str(e)}"
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute notification operations asynchronously."""
        return self._run(query, run_manager)
    
    def _send_meeting_summary(self, query: str) -> str:
        """Send meeting summary via email using client template."""
        try:
            if not self.notification_service:
                return "Error: Notification service not available"
            
            # Parse query to extract meeting information
            attendees = self._extract_attendees(query)
            meeting_title = self._extract_meeting_title(query)
            html_content = self._extract_html_content(query)
            json_data = self._extract_json_data(query)
            
            if not attendees:
                return "Error: No attendees found in query. Please specify attendees."
            
            if not meeting_title:
                meeting_title = "Meeting Summary"
            
            # Create default JSON data if not provided (using template format)
            if not json_data:
                json_data = {
                    "meeting_title": meeting_title,
                    "title": meeting_title,
                    "date_processed": datetime.now().strftime('%B %d, %Y'),
                    "attendees": attendees,
                    "absent": [],
                    "executive_summary": f"Meeting summary for {meeting_title} generated successfully.",
                    "outcomes": [
                        {
                            "decision": "Meeting discussion completed",
                            "owner": "Team",
                            "rationale": "Regular team communication",
                            "context": "Generated from meeting transcript"
                        }
                    ],
                    "open_questions": [],
                    "working_sessions_needed": [],
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "agent_version": "Post meeting-workflow-v1.0"
                    }
                }
            
            # Send the meeting summary using the new signature
            import asyncio
            result = asyncio.run(self.notification_service.send_meeting_summary(
                attendees=attendees,
                meeting_title=meeting_title,
                json_summary=json_data,
                html_summary=html_content if html_content else None
            ))
            
            # Format response
            response = {
                "status": "success" if result.success else "error",
                "message": result.message,
                "delivered_to": result.delivered_to,
                "failed_recipients": result.failed_recipients,
                "meeting_title": meeting_title,
                "template_used": "client_email_template",
                "timestamp": datetime.now().isoformat()
            }
            
            if result.success:
                logger.info(f"Meeting summary sent to {len(result.delivered_to)} attendees using client template")
            else:
                logger.error(f"Failed to send meeting summary: {result.error}")
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error(f"Error sending meeting summary: {e}")
            return f"Error sending meeting summary: {str(e)}"
    
    def _send_admin_alert(self, query: str) -> str:
        """Send admin alert via email."""
        try:
            if not self.notification_service:
                return "Error: Notification service not available"
            
            # Parse query to extract alert information
            subject = self._extract_subject(query)
            message = self._extract_message(query)
            admin_emails = self._extract_admin_emails(query)
            
            if not subject:
                subject = "System Alert"
            
            if not message:
                message = "Alert generated by Meeting Intelligence Agent"
            
            # Send the admin alert
            import asyncio
            result = asyncio.run(self.notification_service.send_admin_alert(
                subject=subject,
                message=message,
                admin_emails=admin_emails
            ))
            
            # Format response
            response = {
                "status": "success" if result.success else "error",
                "message": result.message,
                "delivered_to": result.delivered_to,
                "failed_recipients": result.failed_recipients,
                "subject": subject,
                "timestamp": datetime.now().isoformat()
            }
            
            if result.success:
                logger.info(f"Admin alert sent to {len(result.delivered_to)} administrators")
            else:
                logger.error(f"Failed to send admin alert: {result.error}")
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error(f"Error sending admin alert: {e}")
            return f"Error sending admin alert: {str(e)}"
    
    def _extract_attendees(self, query: str) -> List[str]:
        """Extract attendees from query."""
        attendees = []
        
        # Look for attendees patterns
        if "attendees:" in query:
            attendees_part = query.split("attendees:")[1]
            if "with" in attendees_part:
                attendees_part = attendees_part.split("with")[0]
        elif "to:" in query:
            attendees_part = query.split("to:")[1]
            if "with" in attendees_part:
                attendees_part = attendees_part.split("with")[0]
        else:
            return []
        
        # Extract email addresses from brackets
        if "[" in attendees_part and "]" in attendees_part:
            attendees_str = attendees_part.split("[")[1].split("]")[0]
            attendees = [email.strip().strip("'\"") for email in attendees_str.split(",")]
        
        return [email for email in attendees if email and "@" in email]
    
    def _extract_meeting_title(self, query: str) -> str:
        """Extract meeting title from query."""
        # Look for title patterns
        if "title:" in query:
            title_part = query.split("title:")[1]
            if "with" in title_part:
                title = title_part.split("with")[0].strip().strip("'\"")
            else:
                title = title_part.strip().strip("'\"")
            return title
        elif "meeting:" in query:
            title_part = query.split("meeting:")[1]
            if "with" in title_part:
                title = title_part.split("with")[0].strip().strip("'\"")
            else:
                title = title_part.strip().strip("'\"")
            return title
        
        return ""
    
    def _extract_html_content(self, query: str) -> str:
        """Extract HTML content from query."""
        if "html_content:" in query:
            html_part = query.split("html_content:")[1]
            if "and" in html_part:
                html_content = html_part.split("and")[0].strip().strip("'\"")
            else:
                html_content = html_part.strip().strip("'\"")
            return html_content
        elif "html:" in query:
            html_part = query.split("html:")[1]
            if "and" in html_part:
                html_content = html_part.split("and")[0].strip().strip("'\"")
            else:
                html_content = html_part.strip().strip("'\"")
            return html_content
        
        return ""
    
    def _extract_json_data(self, query: str) -> Dict[str, Any]:
        """Extract JSON data from query."""
        try:
            if "json_data:" in query:
                json_part = query.split("json_data:")[1]
                if "and" in json_part:
                    json_str = json_part.split("and")[0].strip()
                else:
                    json_str = json_part.strip()
                return json.loads(json_str)
            elif "summary_data:" in query:
                json_part = query.split("summary_data:")[1]
                if "and" in json_part:
                    json_str = json_part.split("and")[0].strip()
                else:
                    json_str = json_part.strip()
                return json.loads(json_str)
        except json.JSONDecodeError:
            logger.warning("Failed to parse JSON data from query")
        
        return {}
    
    def _extract_subject(self, query: str) -> str:
        """Extract subject from query."""
        if "subject:" in query:
            subject_part = query.split("subject:")[1]
            if "and" in subject_part:
                subject = subject_part.split("and")[0].strip().strip("'\"")
            else:
                subject = subject_part.strip().strip("'\"")
            return subject
        
        return ""
    
    def _extract_message(self, query: str) -> str:
        """Extract message from query."""
        if "message:" in query:
            message_part = query.split("message:")[1]
            if "and" in message_part:
                message = message_part.split("and")[0].strip().strip("'\"")
            else:
                message = message_part.strip().strip("'\"")
            return message
        
        return ""
    
    def _extract_admin_emails(self, query: str) -> List[str]:
        """Extract admin emails from query."""
        if "admin_emails:" in query:
            emails_part = query.split("admin_emails:")[1]
            if "[" in emails_part and "]" in emails_part:
                emails_str = emails_part.split("[")[1].split("]")[0]
                emails = [email.strip().strip("'\"") for email in emails_str.split(",")]
                return [email for email in emails if email and "@" in email]
        
        return []  # Will use default admin emails
