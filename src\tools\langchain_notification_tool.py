"""LangChain Notification Tool for Post meeting intelligence workflow."""

import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun

from src.services.notification_service import NotificationService, NotificationChannel, NotificationPriority, NotificationRequest
from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES

logger = logging.getLogger(__name__)


class NotificationTool(BaseTool):
    """
    LangChain tool for sending meeting notifications (email, Slack, etc.).
    
    This tool allows the agent to:
    - Send email summaries to meeting attendees
    - Send admin alerts for errors or important events
    - Customize email content for professional distribution
    """
    
    name: str = "notification_tool"
    description: str = AVAILABLE_TOOLS["email_tool"]["description"]
    category: str = AVAILABLE_TOOLS["email_tool"]["category"]

    # Declare service as class variable to avoid Pydantic validation issues
    notification_service: Optional[NotificationService] = None

    def __init__(self):
        super().__init__()
        try:
            self.notification_service = NotificationService()
            logger.info("NotificationTool initialized with email service")
        except Exception as e:
            logger.error(f"Failed to initialize NotificationTool: {e}")
            self.notification_service = None
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute notification operations."""
        try:
            if "send meeting summary" in query.lower():
                return self._send_meeting_summary(query)
            elif "send admin alert" in query.lower():
                return self._send_admin_alert(query)
            elif "email summary" in query.lower():
                return self._send_meeting_summary(query)
            else:
                return self._send_meeting_summary(query)
                
        except Exception as e:
            logger.error(f"Notification tool error: {e}")
            return f"Error sending notification: {str(e)}"
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute notification operations asynchronously."""
        return self._run(query, run_manager)
    
    def _send_meeting_summary(self, query: str) -> str:
        """Send meeting summary via email using client template."""
        try:
            if not self.notification_service:
                return "Error: Notification service not available"
            
            # Parse query to extract meeting information
            attendees = self._extract_attendees(query)
            meeting_title = self._extract_meeting_title(query)
            html_content = self._extract_html_content(query)
            json_data = self._extract_json_data(query)
            
            if not attendees:
                return "Error: No attendees found in query. Please specify attendees."
            
            if not meeting_title:
                meeting_title = "Meeting Summary"
            
            # Create default JSON data if not provided (using template format)
            if not json_data:
                json_data = {
                    "meeting_title": meeting_title,
                    "title": meeting_title,
                    "date_processed": datetime.now().strftime('%B %d, %Y'),
                    "attendees": attendees,
                    "absent": [],
                    "executive_summary": f"Meeting summary for {meeting_title} generated successfully.",
                    "outcomes": [
                        {
                            "decision": "Meeting discussion completed",
                            "owner": "Team",
                            "rationale": "Regular team communication",
                            "context": "Generated from meeting transcript"
                        }
                    ],
                    "open_questions": [],
                    "working_sessions_needed": [],
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "agent_version": "Post meeting-workflow-v1.0"
                    }
                }
            
            # Send the meeting summary using the correct method name
            result = self.notification_service.send_meeting_summary_email(
                recipients=attendees,
                meeting_title=meeting_title,
                meeting_date=datetime.now().strftime('%B %d, %Y'),
                summary_html=html_content if html_content else f"<p>Meeting summary for {meeting_title}</p>",
                summary_json=json_data
            )
            
            # Format response
            response = {
                "status": "success" if result.get("success", False) else "error",
                "message": "Meeting summary sent successfully" if result.get("success", False) else result.get("error", "Unknown error"),
                "delivered_to": attendees if result.get("success", False) else [],
                "failed_recipients": [] if result.get("success", False) else attendees,
                "meeting_title": meeting_title,
                "template_used": "meeting_summary_email",
                "timestamp": datetime.now().isoformat(),
                "result_details": result
            }

            if result.get("success", False):
                logger.info(f"Meeting summary sent to {len(attendees)} attendees using meeting summary template")
            else:
                logger.error(f"Failed to send meeting summary: {result.get('error', 'Unknown error')}")

            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error(f"Error sending meeting summary: {e}")
            return f"Error sending meeting summary: {str(e)}"
    
    def _send_admin_alert(self, query: str) -> str:
        """Send admin alert via email."""
        try:
            if not self.notification_service:
                return "Error: Notification service not available"
            
            # Parse query to extract alert information
            subject = self._extract_subject(query)
            message = self._extract_message(query)
            admin_emails = self._extract_admin_emails(query)
            
            if not subject:
                subject = "System Alert"
            
            if not message:
                message = "Alert generated by Meeting Intelligence Agent"
            
            # Send the admin alert
            result = self.notification_service.send_admin_alert(
                admin_emails=admin_emails if admin_emails else ["<EMAIL>"],
                alert_type="info",
                message=message,
                details={"subject": subject}
            )
            
            # Format response
            response = {
                "status": "success" if result.get("success", False) else "error",
                "message": "Admin alert sent successfully" if result.get("success", False) else result.get("error", "Unknown error"),
                "delivered_to": admin_emails if result.get("success", False) else [],
                "failed_recipients": [] if result.get("success", False) else admin_emails,
                "subject": subject,
                "timestamp": datetime.now().isoformat(),
                "result_details": result
            }

            if result.get("success", False):
                logger.info(f"Admin alert sent to {len(admin_emails)} administrators")
            else:
                logger.error(f"Failed to send admin alert: {result.get('error', 'Unknown error')}")

            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error(f"Error sending admin alert: {e}")
            return f"Error sending admin alert: {str(e)}"
    
    def _extract_attendees(self, query: str) -> List[str]:
        """Extract attendees from query."""
        attendees = []

        # Look for various attendee patterns
        patterns = [
            "attendees:",
            "to:",
            "send email notifications to",
            "send email to",
            "email notifications to"
        ]

        attendees_part = ""
        for pattern in patterns:
            if pattern in query:
                attendees_part = query.split(pattern)[1]
                if "with" in attendees_part:
                    attendees_part = attendees_part.split("with")[0]
                if "and" in attendees_part:
                    attendees_part = attendees_part.split("and")[0]
                break

        if not attendees_part:
            return []

        # Extract email addresses from brackets
        if "[" in attendees_part and "]" in attendees_part:
            attendees_str = attendees_part.split("[")[1].split("]")[0]
            attendees = [email.strip().strip("'\"") for email in attendees_str.split(",")]
        else:
            # Try to extract emails directly from the text
            import re
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            attendees = re.findall(email_pattern, attendees_part)

        return [email for email in attendees if email and "@" in email]
    
    def _extract_meeting_title(self, query: str) -> str:
        """Extract meeting title from query."""
        patterns = [
            "title:",
            "meeting:",
            "subject:",
            "with subject:"
        ]

        for pattern in patterns:
            if pattern in query:
                title_part = query.split(pattern)[1]
                if "with" in title_part and pattern != "with subject:":
                    title = title_part.split("with")[0].strip().strip("'\"")
                elif "and" in title_part:
                    title = title_part.split("and")[0].strip().strip("'\"")
                else:
                    title = title_part.strip().strip("'\"")

                # Clean up the title
                if title and len(title) > 0:
                    return title

        # Try to extract from common patterns
        if "meeting summary" in query.lower():
            # Look for patterns like "product-testing meeting summary"
            import re
            match = re.search(r'(\w+(?:-\w+)*)\s+meeting\s+summary', query.lower())
            if match:
                return match.group(1)

        return "Meeting Summary"
    
    def _extract_html_content(self, query: str) -> str:
        """Extract HTML content from query."""
        patterns = [
            "html_content:",
            "html:",
            "content:",
            "with content:"
        ]

        for pattern in patterns:
            if pattern in query:
                html_part = query.split(pattern)[1]
                # Look for the end of HTML content
                if "with subject:" in html_part:
                    html_content = html_part.split("with subject:")[0].strip().strip("'\"")
                elif "and" in html_part:
                    html_content = html_part.split("and")[0].strip().strip("'\"")
                else:
                    html_content = html_part.strip().strip("'\"")

                # If it looks like HTML, return it
                if "<html" in html_content.lower() or "<div" in html_content.lower():
                    return html_content

        return ""
    
    def _extract_json_data(self, query: str) -> Dict[str, Any]:
        """Extract JSON data from query."""
        try:
            if "json_data:" in query:
                json_part = query.split("json_data:")[1]
                if "and" in json_part:
                    json_str = json_part.split("and")[0].strip()
                else:
                    json_str = json_part.strip()
                return json.loads(json_str)
            elif "summary_data:" in query:
                json_part = query.split("summary_data:")[1]
                if "and" in json_part:
                    json_str = json_part.split("and")[0].strip()
                else:
                    json_str = json_part.strip()
                return json.loads(json_str)
        except json.JSONDecodeError:
            logger.warning("Failed to parse JSON data from query")
        
        return {}
    
    def _extract_subject(self, query: str) -> str:
        """Extract subject from query."""
        if "subject:" in query:
            subject_part = query.split("subject:")[1]
            if "and" in subject_part:
                subject = subject_part.split("and")[0].strip().strip("'\"")
            else:
                subject = subject_part.strip().strip("'\"")
            return subject
        
        return ""
    
    def _extract_message(self, query: str) -> str:
        """Extract message from query."""
        if "message:" in query:
            message_part = query.split("message:")[1]
            if "and" in message_part:
                message = message_part.split("and")[0].strip().strip("'\"")
            else:
                message = message_part.strip().strip("'\"")
            return message
        
        return ""
    
    def _extract_admin_emails(self, query: str) -> List[str]:
        """Extract admin emails from query."""
        if "admin_emails:" in query:
            emails_part = query.split("admin_emails:")[1]
            if "[" in emails_part and "]" in emails_part:
                emails_str = emails_part.split("[")[1].split("]")[0]
                emails = [email.strip().strip("'\"") for email in emails_str.split(",")]
                return [email for email in emails if email and "@" in email]
        
        return []  # Will use default admin emails
