#!/usr/bin/env python3
"""
Meeting Intelligence Agent - Cron Job Setup
Automated setup script for Post meetingworkflow cron job
"""

import os
import sys
import subprocess
import stat
import shutil
from pathlib import Path

# Import app metadata
from src.constants.app import APP_NAME, APP_DESCRIPTION, APP_VERSION

def print_header():
    """Print setup header with Windows-safe characters"""
    print("=" * 60)
    print("🤖 Meeting Intelligence Agent - Cron Job Setup")
    print("   Post meetingWorkflow Automation Every 30 Minutes")
    print("=" * 60)

def print_success(message):
    """Print success message with Windows-safe characters"""
    print(f"[SUCCESS] {message}")

def print_error(message):
    """Print error message with Windows-safe characters"""
    print(f"[ERROR] {message}")

def print_info(message):
    """Print info message with Windows-safe characters"""
    print(f"[INFO] {message}")

def check_requirements():
    """Check if all required files exist"""
    # Get project root directory (two levels up from src/utility)
    project_root = Path(__file__).parent.parent.parent.absolute()
    
    required_files = [
        project_root / 'src' / 'utility' / 'cron_workflow.py',
        project_root / 'src' / 'agents' / 'langchain_meeting_agent.py',
        project_root / 'requirements.txt',
        project_root / 'keys' / 'google-service-account.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(str(file_path))
    
    if missing_files:
        print_error(f"Missing required files: {', '.join(missing_files)}")
        return False
    
    print_success("All required files found")
    return True

def setup_directories():
    """Create necessary directories"""
    # Get project root directory (two levels up from src/utility)
    project_root = Path(__file__).parent.parent.parent.absolute()
    
    directories = [
        project_root / 'logging',
        project_root / 'output',
        project_root / 'output' / 'json',
        project_root / 'output' / 'html'
    ]
    
    for directory in directories:
        if not directory.exists():
            directory.mkdir(parents=True, exist_ok=True)
            print_success(f"Created directory: {directory}")

def make_executable(file_path):
    """Make file executable (Unix-style)"""
    if os.name != 'nt':  # Not Windows
        current_permissions = os.stat(file_path).st_mode
        os.chmod(file_path, current_permissions | stat.S_IEXEC)
        print_success(f"Made {file_path} executable")
    else:
        print_info(f"Windows detected - {file_path} permissions handled by OS")

def test_workflow():
    """Test the cron workflow script"""
    print_info("Testing the cron workflow script...")
    
    # Get project root directory (two levels up from src/utility)
    project_root = Path(__file__).parent.parent.parent.absolute()
    script_path = project_root / 'src' / 'utility' / 'cron_workflow.py'
    
    try:
        # Make cron_workflow.py executable
        make_executable(script_path)
        
        # Test with dry run - run from project root
        if os.name == 'nt':  # Windows
            result = subprocess.run([
                sys.executable, str(script_path), '--test'
            ], capture_output=True, text=True, timeout=60, cwd=project_root)
        else:  # Unix-like
            result = subprocess.run([
                sys.executable, str(script_path), '--test'
            ], capture_output=True, text=True, timeout=60, cwd=project_root)
        
        if result.returncode == 0:
            print_success("Workflow test passed")
            return True
        else:
            print_error(f"Workflow test failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print_error("Workflow test timed out")
        return False
    except Exception as e:
        print_error(f"Workflow test failed: {str(e)}")
        return False

def setup_windows_task():
    """Setup Windows Task Scheduler (alternative to cron)"""
    print_info("Setting up Windows Task Scheduler...")
    
    # Get project root directory (two levels up from src/utility)
    project_root = Path(__file__).parent.parent.parent.absolute()
    
    try:
        # Create a simple batch file to run the workflow
        batch_content = f"""@echo off
cd /d "{project_root}"
python src\\utility\\cron_workflow.py
"""
        
        batch_file = project_root / 'run_meeting_agent.bat'
        with open(batch_file, 'w') as f:
            f.write(batch_content)
        
        print_success("Created run_meeting_agent.bat")
        
        # Instructions for Windows Task Scheduler
        print_info("Windows Task Scheduler setup instructions:")
        print("1. Open Task Scheduler (taskschd.msc)")
        print("2. Create Basic Task...")
        print("3. Name: Meeting Intelligence Agent")
        print("4. Trigger: Daily")
        print("5. Repeat task every: 30 minutes")
        print("6. Action: Start a program")
        print(f"7. Program: {batch_file}")
        print("8. Finish")
        
        return True
        
    except Exception as e:
        print_error(f"Failed to setup Windows task: {str(e)}")
        return False

def setup_unix_cron():
    """Setup Unix cron job"""
    print_info("Setting up Unix cron job...")
    
    # Get project root directory (two levels up from src/utility)
    project_root = Path(__file__).parent.parent.parent.absolute()
    
    try:
        # Create cron entry
        cron_command = f"*/30 * * * * cd {project_root} && python3 src/utility/cron_workflow.py >> logging/cron.log 2>&1"
        
        # Add to crontab
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        current_crontab = result.stdout if result.returncode == 0 else ""
        
        if cron_command not in current_crontab:
            new_crontab = current_crontab + "\n" + cron_command + "\n"
            
            proc = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
            proc.communicate(input=new_crontab)
            
            if proc.returncode == 0:
                print_success("Cron job added successfully")
                return True
            else:
                print_error("Failed to add cron job")
                return False
        else:
            print_info("Cron job already exists")
            return True
            
    except Exception as e:
        print_error(f"Failed to setup cron job: {str(e)}")
        return False

def main():
    """Main setup function"""
    try:
        print_header()
        print_info("Starting cron job setup for Meeting Intelligence Agent...")
        
        # Check requirements
        if not check_requirements():
            sys.exit(1)
        
        # Setup directories
        setup_directories()
        
        # Test workflow
        if not test_workflow():
            print_error("Setup failed: Workflow test failed")
            print_info("Please check your credentials and dependencies")
            sys.exit(1)
        
        # Setup scheduler based on OS
        if os.name == 'nt':  # Windows
            if setup_windows_task():
                print_success("Windows Task Scheduler setup completed")
                print_info("Run the created batch file to test manually")
            else:
                sys.exit(1)
        else:  # Unix-like
            if setup_unix_cron():
                print_success("Unix cron job setup completed")
                print_info("Cron job will run every 30 minutes")
            else:
                sys.exit(1)
        
        print_info("Setup completed successfully!")
        print_info("The Meeting Intelligence Agent will now run every 30 minutes")
        print_info("Check logging/cron.log for execution logs")
        
    except KeyboardInterrupt:
        print_info("Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Setup failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 