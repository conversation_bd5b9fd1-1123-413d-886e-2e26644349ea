@echo off
setlocal enabledelayedexpansion

echo ============================================================
echo Meeting Intelligence Agent - Windows Setup
echo Post meetingWorkflow Automation Every 30 Minutes
echo ============================================================

echo.
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found. Please install Python 3.8+ and add it to PATH.
    pause
    exit /b 1
)

echo [SUCCESS] Python found
echo.

echo [INFO] Installing dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)

echo [SUCCESS] Dependencies installed
echo.

echo [INFO] Running setup script...
python setup_cron.py
if %errorlevel% neq 0 (
    echo [ERROR] Setup failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Setup completed successfully!
echo.
echo To run the workflow manually:
echo   python cron_workflow.py
echo.
echo To test the setup:
echo   python cron_workflow.py --test
echo.
echo To run the batch file created:
echo   run_meeting_agent.bat
echo.
echo Check logs at: logging\cron.log
echo.
pause 