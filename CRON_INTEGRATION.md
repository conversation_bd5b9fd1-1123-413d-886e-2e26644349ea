# 🤖 Meeting Intelligence Agent - Cron Job Integration

This document explains how to set up and use the automated cron job for the Meeting Intelligence Agent's workflow.

## 🎯 Overview

The cron job integration automates the Post meeting intelligence workflow to run **every 30 minutes**:

1. **Identify Meeting & Transcript** - Find meetings and transcripts
2. **Summarize Transcript (AI)** - Generate AI-powered summaries
3. **Generate JSON & HTML Summaries** - Create structured outputs
4. **Email Summaries to Attendees** - Send professional emails
5. **Store Summaries in Google Drive** - Organize in structured folders

## 📁 Cron Job Files

### **Core Files**

- `cron_workflow.py` - Main cron job script (Python)
- `setup_cron.py` - Automated setup script
- `meeting-intelligence-cron.sh` - Alternative bash script
- `meeting-intelligence.service` - SystemD service file

### **Integration Points**

- Uses `src/agents/langchain_meeting_agent.py`
- Leverages existing 5-tool architecture
- Shares same credentials and configuration

## 🚀 Quick Setup (Automated)

### **Windows Users (Recommended)**

```batch
# Run the Windows setup batch file
setup_windows.bat
```

This will:

- ✅ Check Python installation
- ✅ Install dependencies
- ✅ Run the setup script
- ✅ Create Windows Task Scheduler instructions
- ✅ Configure logging

**Or run manually:**

```bash
python setup_cron.py
```

### **Linux/Mac Users**

```bash
# Run the automated setup script
python setup_cron.py
```

This will:

- ✅ Check all dependencies
- ✅ Test the workflow
- ✅ Install the cron job
- ✅ Configure logging
- ✅ Verify installation

### **Option 2: Manual Setup**

```bash
# 1. Make scripts executable
chmod +x cron_workflow.py
chmod +x meeting-intelligence-cron.sh

# 2. Test the workflow
python cron_workflow.py

# 3. Install cron job manually
crontab -e
# Add this line:
*/30 * * * * cd /d/sopna_elevatio_ai/meeting-intelligence-agent && python cron_workflow.py >> logging/cron.log 2>&1
```

## 📋 Cron Job Configuration

### **Schedule: Every 30 Minutes**

```bash
*/30 * * * * cd /d/sopna_elevatio_ai/meeting-intelligence-agent && python cron_workflow.py >> logging/cron.log 2>&1
```

### **What This Means:**

- `*/30 * * * *` - Every 30 minutes
- `cd /d/sopna_elevatio_ai/meeting-intelligence-agent` - Change to project directory
- `python cron_workflow.py` - Run the workflow script
- `>> logging/cron.log 2>&1` - Log output and errors

## 🔧 Configuration

### **Environment Variables (Auto-configured)**

```bash
GOOGLE_PROJECT_ID=elevation-agent-dev
GOOGLE_APPLICATION_CREDENTIALS=keys/google-service-account.json
VERTEX_AI_LOCATION=us-central1
GMAIL_CREDENTIALS_PATH=keys/gmail-credentials.json
GMAIL_TOKEN_PATH=keys/gmail-token.json
```

### **Required Files**

```
meeting-intelligence-agent/
├── keys/
│   ├── google-service-account.json     ✅ Service account credentials
│   ├── gmail-credentials.json          ✅ Gmail OAuth credentials
│   └── gmail-token.json                ✅ Gmail access token
├── src/agents/langchain_meeting_agent.py ✅ Main workflow
├── cron_workflow.py                    ✅ Cron job script
└── logging/                            ✅ Log directory (auto-created)
```

## 📊 Monitoring & Logging

### **Log Files**

```bash
# Main cron job logs
tail -f logging/cron.log

# Workflow statistics
tail -f logging/workflow_stats.log

# API server logs (if running)
tail -f logging/api.log
```

### **Check Cron Job Status**

```bash
# View installed cron jobs
crontab -l

# Check if cron service is running
systemctl status cron
```

### **Monitoring Commands**

```bash
# Real-time log monitoring
tail -f logging/cron.log

# Check recent executions
grep "Meeting Intelligence Automated Workflow" logging/cron.log | tail -10

# View workflow statistics
cat logging/workflow_stats.log
```

## 🪟 Windows Task Scheduler Setup

Since Windows doesn't have cron, the setup script creates a batch file and provides instructions for Windows Task Scheduler:

### **After running setup_cron.py on Windows:**

1. **A batch file is created:** `run_meeting_agent.bat`
2. **Follow these Task Scheduler instructions:**
   - Open Task Scheduler (`Win + R`, type `taskschd.msc`)
   - Click "Create Basic Task..."
   - **Name:** Meeting Intelligence Agent
   - **Trigger:** Daily
   - **Repeat task every:** 30 minutes
   - **Action:** Start a program
   - **Program:** Browse to `run_meeting_agent.bat`
   - Click **Finish**

### **Alternative Windows Methods:**

**Option 1: PowerShell Scheduled Job**

```powershell
# Create a scheduled job (PowerShell 5.1)
$trigger = New-JobTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 30) -RepetitionDuration (New-TimeSpan -Days 365)
$action = New-ScheduledJobOption -StartIfOnBattery -ContinueIfGoingOnBattery
Register-ScheduledJob -Name "MeetingIntelligenceAgent" -ScriptBlock {
    Set-Location "D:\sopna_elevatio_ai\meeting-intelligence-agent"
    python cron_workflow.py
} -Trigger $trigger -ScheduledJobOption $action
```

**Option 2: Windows Service**

```cmd
# Use the provided service file
sc create MeetingIntelligenceAgent binpath="D:\sopna_elevatio_ai\meeting-intelligence-agent\meeting-intelligence.service"
sc start MeetingIntelligenceAgent
```

### **Windows Monitoring Commands:**

```cmd
# Check Windows task status
schtasks /query /tn "Meeting Intelligence Agent"

# View logs
type logging\cron.log
type logging\workflow_stats.log

# Test manually
python cron_workflow.py --test
run_meeting_agent.bat
```

## 🔄 Workflow Execution Flow

### **Every 30 Minutes:**

```
Cron Trigger
    ↓
Environment Setup (elevation-agent-dev)
    ↓
Dependency Check (credentials, src files)
    ↓
Import LangChain Agent
    ↓
Execute Post meetingWorkflow:
    1. CalendarTool → Find meetings
    2. DriveTool → Find transcripts
    3. SummarizerTool → AI summarization
    4. NotificationTool → Email attendees
    5. DriveTool → Store in Drive
    ↓
Log Results & Statistics
```

## 🛠 Management Commands

### **Install Cron Job**

```bash
python setup_cron.py
```

### **Remove Cron Job**

```bash
crontab -e
# Delete the line containing "cron_workflow.py"
```

### **Test Workflow**

```bash
# Test without cron
python cron_workflow.py

# Test specific function
python -c "from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow; import asyncio; print(asyncio.run(run_autonomous_meeting_workflow()))"
```

### **Restart Cron Service**

```bash
sudo systemctl restart cron
```

## 📈 Expected Output

### **Successful Execution Log**

```
2025-01-13 10:30:01 - __main__ - INFO - 🎯 Meeting Intelligence Automated Workflow started at 2025-01-13 10:30:01
2025-01-13 10:30:01 - __main__ - INFO - 📁 Working directory: /d/sopna_elevatio_ai/meeting-intelligence-agent
2025-01-13 10:30:01 - __main__ - INFO - Set environment variable: GOOGLE_PROJECT_ID
2025-01-13 10:30:01 - __main__ - INFO - ✅ All required credential files are present
2025-01-13 10:30:01 - __main__ - INFO - ✅ Source directory found
2025-01-13 10:30:02 - __main__ - INFO - 🚀 Starting automated Post meeting intelligence workflow
2025-01-13 10:30:45 - __main__ - INFO - ✅ Post meetingworkflow completed successfully
2025-01-13 10:30:45 - __main__ - INFO - 📊 Workflow Statistics: {'timestamp': '2025-01-13T10:30:45', 'duration_seconds': 43.2, 'status': 'success', 'workflow_type': '5_step_automated'}
2025-01-13 10:30:45 - __main__ - INFO - 🎉 Automated workflow completed successfully in 0:00:43.2
```

## 🔧 Troubleshooting

### **Common Issues**

**Q: Cron job not running**

```bash
# Check if cron service is active
systemctl status cron

# Check cron logs
tail -f /var/log/cron

# Verify cron job is installed
crontab -l | grep cron_workflow
```

**Q: Import errors**

```bash
# Test imports manually
cd /d/sopna_elevatio_ai/meeting-intelligence-agent
python -c "import sys; sys.path.insert(0, 'src'); from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow"
```

**Q: Permission errors**

```bash
# Make scripts executable
chmod +x cron_workflow.py
chmod +x meeting-intelligence-cron.sh
```

**Q: Missing credentials**

```bash
# Check credential files exist
ls -la keys/
# Should show: google-service-account.json, gmail-credentials.json, gmail-token.json
```

## 🎛 Integration with API Server

### **Hybrid Approach (Recommended)**

You can run both the API server AND the cron job:

```bash
# Terminal 1: Start API server
python start_api.py

# Terminal 2: Install cron job
python setup_cron.py
```

### **Benefits of Hybrid Approach:**

- **API Server**: Interactive control, manual triggers, monitoring
- **Cron Job**: Reliable automation, system-level scheduling
- **Redundancy**: Multiple ways to execute workflows

### **API vs Cron Job**

| Feature            | API Scheduler       | Cron Job          |
| ------------------ | ------------------- | ----------------- |
| **Reliability**    | Depends on server   | System-level      |
| **Manual Control** | ✅ Easy             | ❌ Command line   |
| **Monitoring**     | ✅ Web interface    | ✅ Log files      |
| **Auto-restart**   | ❌ Server dependent | ✅ System managed |
| **Setup**          | ✅ Simple           | ⚠️ Requires setup |

## 🚀 Production Deployment

### **Recommended Setup:**

1. **SystemD Service** for API server (optional)
2. **Cron Job** for workflow automation
3. **Log Rotation** for long-term operation

```bash
# 1. Install cron job
python setup_cron.py

# 2. Set up log rotation (optional)
sudo logrotate -d /etc/logrotate.d/meeting-intelligence

# 3. Monitor execution
tail -f logging/cron.log
```

## 🎯 Integration Complete!

Your Meeting Intelligence Agent now has:

✅ **Automated Post meetingWorkflow** (every 30 minutes)  
✅ **System-Level Scheduling** (cron job)  
✅ **Comprehensive Logging** (execution tracking)  
✅ **Statistics Monitoring** (performance metrics)  
✅ **Error Handling** (graceful failure recovery)  
✅ **Easy Management** (setup/remove scripts)

**The cron job is fully integrated with your existing codebase and will automatically process meetings every 30 minutes!** 🎉
