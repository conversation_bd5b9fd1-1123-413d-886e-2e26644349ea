# Database Setup Guide for Meeting Intelligence Agent

## 🚀 Quick Setup Options

### Option 1: Local MySQL Database (Recommended for Development)

#### Step 1: Install MySQL

```bash
# Windows (using Chocolatey)
choco install mysql

# Or download from: https://dev.mysql.com/downloads/mysql/
```

#### Step 2: Create Database and User

```sql
-- Connect to MySQL as root
mysql -u root -p

-- Create database
CREATE DATABASE `meeting-intelligence`;

-- Create user
CREATE USER 'meeting_agent'@'localhost' IDENTIFIED BY 'your_secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON `meeting-intelligence`.* TO 'meeting_agent'@'localhost';
FLUSH PRIVILEGES;

-- Exit
EXIT;
```

#### Step 3: Update Configuration

Create or update `.env` file:

```env
# Database Configuration
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=meeting_agent
MYS<PERSON>_PASSWORD=your_secure_password
MYSQL_DATABASE=meeting-intelligence

# Other configurations
GOOGLE_APPLICATION_CREDENTIALS=./meeting-intelligence-agent/keys/google-service-account.json
GMAIL_CREDENTIALS_PATH=./meeting-intelligence-agent/keys/gmail-credentials.json
VERTEX_AI_LOCATION=us-central1
```

### Option 2: Google Cloud SQL (Production)

#### Prerequisites

1. **Install Google Cloud CLI:**

   ```bash
   # Windows
   # Download from: https://cloud.google.com/sdk/docs/install

   # Or use PowerShell:
   (New-Object Net.WebClient).DownloadFile("https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe", "$env:Temp\GoogleCloudSDKInstaller.exe")
   Start-Process -FilePath "$env:Temp\GoogleCloudSDKInstaller.exe" -ArgumentList "/S"
   ```

2. **Authenticate and Set Project:**
   ```bash
   gcloud auth login
   gcloud config set project elevation-agent-dev
   ```

#### Step 1: Find Your SQL Instance

```bash
# List all SQL instances
gcloud sql instances list --project elevation-agent-dev
```

#### Step 2: Get Instance Details

```bash
# Replace INSTANCE_NAME with your actual instance name
gcloud sql instances describe INSTANCE_NAME --project elevation-agent-dev --format json
```

#### Step 3: Add Your IP to Authorized Networks

```bash
# Get your current IP
curl ifconfig.me

# Add your IP to authorized networks
gcloud sql instances patch INSTANCE_NAME \
  --authorized-networks YOUR_IP/32 \
  --authorized-network-name "meeting-agent-dev"
```

#### Step 4: Create Database

```bash
# Connect to your instance
gcloud sql connect INSTANCE_NAME --user=root

# In the MySQL prompt:
CREATE DATABASE `meeting-intelligence`;
EXIT;
```

#### Step 5: Update Configuration

Update your `.env` file with the correct connection details:

```env
MYSQL_HOST=YOUR_INSTANCE_IP
MYSQL_PORT=3306
MYSQL_USERNAME=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=meeting-intelligence
```

### Option 3: Use the Configuration Script

If you have Google Cloud CLI installed:

```bash
# Install dependencies
pip install -r gcloud_sql_requirements.txt

# Run the configuration script
python configure_google_cloud_sql.py
```

## 🔧 Manual Configuration Steps

### Step 1: Install Python Dependencies

```bash
pip install pymysql cryptography
```

### Step 2: Test Database Connection

```bash
python test_db_connection.py
```

### Step 3: Create Database Tables

```bash
# Set PYTHONPATH
$env:PYTHONPATH = "./meeting-intelligence-agent"

# Run database checker to create tables
python -c "from src.utility.db_checker import DatabaseChecker; checker = DatabaseChecker(); checker.create_tables_from_models()"
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Connection Refused (Error 10061)

**Cause:** Server not reachable or port blocked
**Solutions:**

- Check if MySQL server is running
- Verify firewall settings
- Check if port 3306 is open

#### 2. Access Denied

**Cause:** Wrong credentials or insufficient permissions
**Solutions:**

- Verify username and password
- Check user permissions
- Ensure database exists

#### 3. SSL Connection Required

**Cause:** Google Cloud SQL requires SSL
**Solutions:**

- Add SSL parameters to connection string
- Use Cloud SQL Proxy for secure connections

#### 4. IP Not Authorized

**Cause:** Your IP not in authorized networks
**Solutions:**

- Add your IP to Google Cloud SQL authorized networks
- Use Cloud SQL Proxy

### Google Cloud SQL Specific Issues

#### 1. Instance Not Found

```bash
# Check if instance exists
gcloud sql instances list --project elevation-agent-dev
```

#### 2. Wrong Project

```bash
# Set correct project
gcloud config set project elevation-agent-dev
```

#### 3. Authentication Issues

```bash
# Re-authenticate
gcloud auth login
gcloud auth application-default login
```

## 🔒 Security Best Practices

### For Local Development

1. Use strong passwords
2. Don't expose MySQL to external networks
3. Use environment variables for sensitive data

### For Google Cloud SQL

1. Use Cloud SQL Proxy for secure connections
2. Limit authorized networks to specific IPs
3. Use service accounts with minimal permissions
4. Enable SSL connections

## 📊 Database Schema

The application creates these tables automatically:

- `meetings` - Meeting information
- `transcripts` - Meeting transcripts
- `meeting_summaries` - AI-generated summaries
- `agent_runs` - Workflow execution logs
- `email_logs` - Email notification tracking
- `feedback` - User feedback
- `system_config` - System configuration
- `notification_logs` - General notifications
- `processing_queue` - Meeting processing queue

## ✅ Verification

After setup, verify everything works:

```bash
# Test connection
python test_db_connection.py

# Run the agent
python run_agent.py
```

## 🆘 Need Help?

If you encounter issues:

1. Check the troubleshooting section above
2. Review error logs in the console output
3. Verify all environment variables are set correctly
4. Ensure database tables are created successfully
