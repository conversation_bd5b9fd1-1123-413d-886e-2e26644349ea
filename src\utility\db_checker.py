"""
Database Connection Checker and Table Inspector
Tests Cloud SQL connection and displays table information
"""

import os
import sys
from pathlib import Path
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import SQLAlchemyError
import logging
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / '.env')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import health endpoints and response status
from src.constants.app import HEALTH_CHECK_ENDPOINTS, RESPONSE_STATUS

class DatabaseChecker:
    """Database connection and table checker for Cloud SQL."""
    
    def __init__(self):
        self.host = os.getenv('MYSQL_HOST', '*************')
        self.port = os.getenv('MYSQL_PORT', '3306')
        self.username = os.getenv('MYSQL_USERNAME', 'root')
        self.password = os.getenv('MYSQL_PASSWORD', '"$=a-^eG@(u;J;iBx - db p.w"')
        self.database = os.getenv('MYSQL_DATABASE', 'meeting-intelligence')
        
        # Clean password (remove quotes if present)
        if self.password.startswith('"') and self.password.endswith('"'):
            self.password = self.password[1:-1]
        
        # Construct database URL
        self.database_url = f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        
        # Create engine
        self.engine = None
        self._create_engine()
    
    def _create_engine(self):
        """Create SQLAlchemy engine with proper configuration for Cloud SQL."""
        try:
            self.engine = create_engine(
                self.database_url,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=True,
                pool_recycle=300,
                echo=False,
                connect_args={
                    "charset": "utf8mb4",
                    "connect_timeout": 60,
                    "read_timeout": 60,
                    "write_timeout": 60
                }
            )
            logger.info("Database engine created successfully")
        except Exception as e:
            logger.error(f"Failed to create database engine: {e}")
            self.engine = None
    
    def test_connection(self):
        """Test database connection."""
        if not self.engine:
            logger.error("Database engine not available")
            return False
        
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                
                if test_value == 1:
                    logger.info("✅ Database connection successful!")
                    return True
                else:
                    logger.error("❌ Database connection test failed")
                    return False
                    
        except SQLAlchemyError as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during connection test: {e}")
            return False
    
    def get_database_info(self):
        """Get basic database information."""
        if not self.engine:
            return None
        
        try:
            with self.engine.connect() as connection:
                # Get database version
                version_result = connection.execute(text("SELECT VERSION() as version"))
                version = version_result.fetchone()[0]
                
                # Get database name
                db_result = connection.execute(text("SELECT DATABASE() as db_name"))
                db_name = db_result.fetchone()[0]
                
                # Get current user
                user_result = connection.execute(text("SELECT USER() as current_user"))
                current_user = user_result.fetchone()[0]
                
                return {
                    "version": version,
                    "database": db_name,
                    "user": current_user,
                    "host": self.host,
                    "port": self.port
                }
                
        except Exception as e:
            logger.error(f"Failed to get database info: {e}")
            return None
    
    def list_tables(self):
        """List all tables in the database."""
        if not self.engine:
            return []
        
        try:
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            return tables
        except Exception as e:
            logger.error(f"Failed to list tables: {e}")
            return []
    
    def get_table_info(self, table_name):
        """Get detailed information about a specific table."""
        if not self.engine:
            return None
        
        try:
            inspector = inspect(self.engine)
            
            # Get columns
            columns = inspector.get_columns(table_name)
            
            # Get indexes
            indexes = inspector.get_indexes(table_name)
            
            # Get foreign keys
            foreign_keys = inspector.get_foreign_keys(table_name)
            
            # Get primary key
            primary_key = inspector.get_primary_keys(table_name)
            
            # Get table comment
            table_comment = inspector.get_table_comment(table_name)
            
            return {
                "table_name": table_name,
                "columns": columns,
                "indexes": indexes,
                "foreign_keys": foreign_keys,
                "primary_key": primary_key,
                "comment": table_comment
            }
            
        except Exception as e:
            logger.error(f"Failed to get table info for {table_name}: {e}")
            return None
    
    def get_table_row_count(self, table_name):
        """Get row count for a table."""
        if not self.engine:
            return 0
        
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(f"SELECT COUNT(*) as count FROM `{table_name}`"))
                count = result.fetchone()[0]
                return count
        except Exception as e:
            logger.error(f"Failed to get row count for {table_name}: {e}")
            return 0
    
    def create_tables_from_models(self):
        """Create tables from SQLAlchemy models."""
        try:
            from src.constants.tables import Base
            
            logger.info("Creating tables from models...")
            Base.metadata.create_all(bind=self.engine)
            logger.info("✅ Tables created successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create tables: {e}")
            return False
    
    def display_comprehensive_report(self):
        """Display a comprehensive database report."""
        print("\n" + "="*80)
        print("DATABASE CONNECTION AND TABLES REPORT")
        print("="*80)
        
        # Test connection
        print("\n🔍 TESTING CONNECTION...")
        connection_success = self.test_connection()
        
        if not connection_success:
            print("❌ Cannot proceed - database connection failed")
            return False
        
        # Get database info
        print("\n📊 DATABASE INFORMATION:")
        db_info = self.get_database_info()
        if db_info:
            print(f"   Host: {db_info['host']}:{db_info['port']}")
            print(f"   Database: {db_info['database']}")
            print(f"   User: {db_info['user']}")
            print(f"   Version: {db_info['version']}")
        
        # List tables
        print("\n📋 EXISTING TABLES:")
        tables = self.list_tables()
        
        if not tables:
            print("   No tables found in database")
            print("\n🔧 CREATING TABLES FROM MODELS...")
            if self.create_tables_from_models():
                tables = self.list_tables()
            else:
                return False
        
        if tables:
            print(f"   Found {len(tables)} tables:")
            for i, table in enumerate(tables, 1):
                row_count = self.get_table_row_count(table)
                print(f"   {i:2d}. {table:<30} ({row_count:,} rows)")
        
        # Detailed table information
        print("\n📝 DETAILED TABLE INFORMATION:")
        for table in tables:
            print(f"\n   TABLE: {table}")
            print(f"   {'='*50}")
            
            table_info = self.get_table_info(table)
            if table_info:
                # Columns
                print("   COLUMNS:")
                for col in table_info['columns']:
                    nullable = "NULL" if col['nullable'] else "NOT NULL"
                    default = f" DEFAULT {col['default']}" if col['default'] else ""
                    print(f"     - {col['name']:<25} {str(col['type']):<20} {nullable}{default}")
                
                # Primary Key
                if table_info['primary_key']:
                    print(f"   PRIMARY KEY: {', '.join(table_info['primary_key'])}")
                
                # Foreign Keys
                if table_info['foreign_keys']:
                    print("   FOREIGN KEYS:")
                    for fk in table_info['foreign_keys']:
                        print(f"     - {fk['constrained_columns']} -> {fk['referred_table']}.{fk['referred_columns']}")
                
                # Indexes
                if table_info['indexes']:
                    print("   INDEXES:")
                    for idx in table_info['indexes']:
                        unique = "UNIQUE " if idx['unique'] else ""
                        print(f"     - {unique}{idx['name']}: {', '.join(idx['column_names'])}")
        
        print("\n" + "="*80)
        print("✅ DATABASE REPORT COMPLETED SUCCESSFULLY")
        print("="*80)
        
        return True


def main():
    """Main function to run database checks."""
    print("Starting Database Connection Check...")
    
    # Create checker instance
    checker = DatabaseChecker()
    
    # Display comprehensive report
    success = checker.display_comprehensive_report()
    
    if success:
        print("\n🎉 All checks completed successfully!")
        return 0
    else:
        print("\n❌ Some checks failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
