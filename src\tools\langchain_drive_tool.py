"""LangChain Drive Tool for autonomous meeting intelligence."""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path

from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
from googleapiclient.discovery import build

from src.utility.google_auth import GoogleAuthenticator
# Import tool configs and categories
from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES

logger = logging.getLogger(__name__)


class DriveTool(BaseTool):
    """
    LangChain tool for Google Drive operations.
    
    This tool allows the agent to:
    - Search for transcript files in Google Drive
    - Read file contents
    - Upload processed summaries
    - Organize files in structured folders
    - Check file metadata and processing status
    """
    
    name: str = "drive_tool"
    description: str = AVAILABLE_TOOLS["drive_tool"]["description"]
    category: str = AVAILABLE_TOOLS["drive_tool"]["category"]

    # Declare auth as a class variable to avoid Pydantic validation issues
    auth: Optional[GoogleAuthenticator] = None
    drive_service: Optional[Any] = None

    def __init__(self, auth: GoogleAuthenticator, **kwargs):
        super().__init__(**kwargs)
        self.auth = auth
        self.drive_service = build('drive', 'v3', credentials=auth.credentials)
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute Drive operations."""
        try:
            if "find" in query.lower() or "search" in query.lower():
                return self._search_files(query)
            elif "read" in query.lower() or "content" in query.lower():
                return self._read_file(query)
            elif "upload" in query.lower():
                return self._upload_file(query)
            elif "create folder" in query.lower():
                return self._create_folder(query)
            elif "check processed" in query.lower():
                return self._check_processed_status(query)
            else:
                return self._search_files(query)
                
        except Exception as e:
            logger.error(f"Drive tool error: {e}")
            return f"Error accessing Google Drive: {str(e)}"
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute Drive operations asynchronously."""
        return self._run(query, run_manager)
    
    def _search_files(self, query: str) -> str:
        """Search for files in Google Drive."""
        try:
            # Parse search criteria from query
            search_terms = []
            time_filter = None
            
            if "transcript" in query.lower():
                # Support multiple file formats for transcripts
                file_extensions = [
                    "name contains '.txt'",           # Plain text files
                    "name contains '.docx'",          # Microsoft Word documents
                    "name contains '.doc'",           # Legacy Word documents
                    "name contains '.pdf'",           # PDF documents
                    "name contains '.rtf'",           # Rich Text Format
                    "name contains '.odt'",           # OpenDocument Text
                    "name contains '.transcript'",    # Transcript files
                    "name contains '.vtt'",           # WebVTT subtitle files
                    "name contains '.srt'",           # SubRip subtitle files
                    "name contains '.json'",          # JSON transcript files
                    "name contains '.csv'",           # CSV transcript files
                    "name contains '.tsv'",   
                    "name contains '.Notes by Gemini'",        # Tab-separated values
                    "mimeType contains 'text'",       # Any text-based files
                    "mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'",  # .docx
                    "mimeType = 'application/msword'",  # .doc
                    "mimeType = 'application/pdf'",     # .pdf
                    "mimeType = 'application/rtf'",     # .rtf
                    "mimeType = 'application/vnd.oasis.opendocument.text'"  # .odt
                ]
                search_terms.append("(" + " or ".join(file_extensions) + ")")
            
            if "last hour" in query.lower():
                hour_ago = datetime.now() - timedelta(hours=1)
                time_filter = f"modifiedTime >= '{hour_ago.isoformat()}Z'"
            elif "last" in query.lower() and "hours" in query.lower():
                try:
                    # Extract number of hours
                    words = query.split()
                    hours = 1
                    for i, word in enumerate(words):
                        if word.isdigit() and i + 1 < len(words) and "hour" in words[i + 1]:
                            hours = int(word)
                            break
                    hours_ago = datetime.now() - timedelta(hours=hours)
                    time_filter = f"modifiedTime >= '{hours_ago.isoformat()}Z'"
                except:
                    pass
            
            # Build search query
            query_parts = []
            if search_terms:
                query_parts.extend(search_terms)
            if time_filter:
                query_parts.append(time_filter)
            
            search_query = " and ".join(query_parts) if query_parts else "mimeType contains 'text'"
            
            # Execute search
            results = self.drive_service.files().list(
                q=search_query,
                pageSize=50,
                fields="nextPageToken, files(id, name, mimeType, modifiedTime, size, parents, webViewLink)"
            ).execute()
            
            files = results.get('files', [])
            
            if not files:
                return "No files found matching the search criteria."
            
            # Format results
            file_summaries = []
            for file in files:
                summary = {
                    "id": file.get("id"),
                    "name": file.get("name"),
                    "mimeType": file.get("mimeType"),
                    "modifiedTime": file.get("modifiedTime"),
                    "size": file.get("size"),
                    "webViewLink": file.get("webViewLink"),
                    "parents": file.get("parents", [])
                }
                file_summaries.append(summary)
            
            result = {
                "status": "success",
                "search_query": search_query,
                "files_found": len(files),
                "files": file_summaries
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error searching files: {e}")
            return f"Error searching Google Drive: {str(e)}"
    
    def _read_file(self, query: str) -> str:
        """Read file content from Google Drive."""
        try:
            # Extract file ID from query
            file_id = None
            if "file_id:" in query:
                file_id = query.split("file_id:")[1].strip().split()[0]
            elif "id:" in query:
                file_id = query.split("id:")[1].strip().split()[0]
            
            if not file_id:
                return "Please provide a file ID. Format: 'read file content for file_id: <id>'"
            
            # Get file metadata
            file_metadata = self.drive_service.files().get(fileId=file_id).execute()
            file_name = file_metadata.get("name", "")
            mime_type = file_metadata.get("mimeType", "")

            # Handle different file formats
            content_text = self._extract_text_from_file(file_id, file_name, mime_type)
            
            # Limit content length for response
            max_length = 5000
            if len(content_text) > max_length:
                content_preview = content_text[:max_length] + "... [TRUNCATED]"
            else:
                content_preview = content_text
            
            result = {
                "status": "success",
                "file_id": file_id,
                "file_name": file_metadata.get("name"),
                "file_size": len(content_text),
                "content_preview": content_preview,
                "full_content_available": True
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error reading file: {e}")
            return f"Error reading file from Google Drive: {str(e)}"

    def _extract_text_from_file(self, file_id: str, file_name: str, mime_type: str) -> str:
        """Extract text content from different file formats."""
        try:
            # For Google Docs, use export instead of download
            if mime_type == 'application/vnd.google-apps.document':
                # Export Google Doc as plain text
                file_content = self.drive_service.files().export_media(
                    fileId=file_id,
                    mimeType='text/plain'
                ).execute()
                return file_content.decode('utf-8')

            # For Microsoft Word documents (.docx)
            elif mime_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_docx_text(file_content)

            # For legacy Word documents (.doc)
            elif mime_type == 'application/msword':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_doc_text(file_content)

            # For PDF files
            elif mime_type == 'application/pdf':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_pdf_text(file_content)

            # For RTF files
            elif mime_type == 'application/rtf':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_rtf_text(file_content)

            # For OpenDocument Text (.odt)
            elif mime_type == 'application/vnd.oasis.opendocument.text':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_odt_text(file_content)

            # For plain text files and other text formats
            else:
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()

                # Try different encodings
                try:
                    return file_content.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        return file_content.decode('latin-1')
                    except UnicodeDecodeError:
                        try:
                            return file_content.decode('cp1252')
                        except:
                            return f"[Binary content - {len(file_content)} bytes] - Unable to decode as text"

        except Exception as e:
            logger.error(f"Error extracting text from {file_name}: {e}")
            return f"Error extracting text from {file_name}: {str(e)}"

    def _extract_docx_text(self, file_content: bytes) -> str:
        """Extract text from .docx files."""
        try:
            # Try to use python-docx if available
            import io
            from zipfile import ZipFile
            import xml.etree.ElementTree as ET

            # Simple DOCX text extraction
            with ZipFile(io.BytesIO(file_content)) as docx:
                with docx.open('word/document.xml') as doc_xml:
                    tree = ET.parse(doc_xml)
                    root = tree.getroot()

                    # Extract text from all text nodes
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)

                    return '\n'.join(text_content)
        except Exception as e:
            logger.warning(f"Could not extract DOCX text: {e}")
            return f"[DOCX file - {len(file_content)} bytes] - Install python-docx for better text extraction"

    def _extract_doc_text(self, file_content: bytes) -> str:
        """Extract text from legacy .doc files."""
        try:
            # Basic text extraction for .doc files
            # This is a simplified approach - for better extraction, use python-docx2txt
            text = file_content.decode('utf-8', errors='ignore')
            # Remove common binary artifacts
            import re
            text = re.sub(r'[^\x20-\x7E\n\r\t]', '', text)
            return text
        except Exception as e:
            logger.warning(f"Could not extract DOC text: {e}")
            return f"[DOC file - {len(file_content)} bytes] - Install python-docx2txt for better text extraction"

    def _extract_pdf_text(self, file_content: bytes) -> str:
        """Extract text from PDF files."""
        try:
            # Try to use PyPDF2 or pdfplumber if available
            import io
            # Simple PDF text extraction would require PyPDF2 or similar
            return f"[PDF file - {len(file_content)} bytes] - Install PyPDF2 or pdfplumber for PDF text extraction"
        except Exception as e:
            logger.warning(f"Could not extract PDF text: {e}")
            return f"[PDF file - {len(file_content)} bytes] - PDF text extraction not available"

    def _extract_rtf_text(self, file_content: bytes) -> str:
        """Extract text from RTF files."""
        try:
            # Basic RTF text extraction
            text = file_content.decode('utf-8', errors='ignore')
            # Remove RTF control codes (basic approach)
            import re
            text = re.sub(r'\\[a-z]+\d*\s?', '', text)  # Remove RTF commands
            text = re.sub(r'[{}]', '', text)  # Remove braces
            return text.strip()
        except Exception as e:
            logger.warning(f"Could not extract RTF text: {e}")
            return f"[RTF file - {len(file_content)} bytes] - RTF text extraction failed"

    def _extract_odt_text(self, file_content: bytes) -> str:
        """Extract text from OpenDocument Text files."""
        try:
            # ODT files are ZIP archives with XML content
            import io
            from zipfile import ZipFile
            import xml.etree.ElementTree as ET

            with ZipFile(io.BytesIO(file_content)) as odt:
                with odt.open('content.xml') as content_xml:
                    tree = ET.parse(content_xml)
                    root = tree.getroot()

                    # Extract text from all text nodes
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)

                    return '\n'.join(text_content)
        except Exception as e:
            logger.warning(f"Could not extract ODT text: {e}")
            return f"[ODT file - {len(file_content)} bytes] - ODT text extraction failed"
    
    def _upload_file(self, query: str) -> str:
        """Upload file to Google Drive."""
        try:
            # This is a placeholder for upload functionality
            # In practice, the agent would need to specify content and destination
            return "Upload functionality requires specific content and destination folder. Please use the drive_organizer service for structured uploads."
            
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return f"Error uploading to Google Drive: {str(e)}"
    
    def _create_folder(self, query: str) -> str:
        """Create folder structure in Google Drive."""
        try:
            # Extract folder path from query
            folder_path = None
            if "folder:" in query:
                folder_path = query.split("folder:")[1].strip()
            elif "path:" in query:
                folder_path = query.split("path:")[1].strip()
            
            if not folder_path:
                return "Please provide a folder path. Format: 'create folder: /path/to/folder'"
            
            # Create folder structure
            folder_id = self._create_folder_structure(folder_path)
            
            result = {
                "status": "success",
                "folder_path": folder_path,
                "folder_id": folder_id,
                "message": "Folder structure created successfully"
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error creating folder: {e}")
            return f"Error creating folder in Google Drive: {str(e)}"
    
    def _create_folder_structure(self, folder_path: str) -> str:
        """Create nested folder structure and return the final folder ID."""
        # This is a simplified implementation
        # In practice, you'd need to handle the full folder creation logic
        parts = folder_path.strip('/').split('/')
        parent_id = 'root'
        
        for part in parts:
            if part:
                # Check if folder exists
                existing = self.drive_service.files().list(
                    q=f"name='{part}' and '{parent_id}' in parents and mimeType='application/vnd.google-apps.folder'",
                    fields="files(id, name)"
                ).execute()
                
                if existing.get('files'):
                    parent_id = existing['files'][0]['id']
                else:
                    # Create new folder
                    folder_metadata = {
                        'name': part,
                        'mimeType': 'application/vnd.google-apps.folder',
                        'parents': [parent_id]
                    }
                    folder = self.drive_service.files().create(body=folder_metadata).execute()
                    parent_id = folder.get('id')
        
        return parent_id
    
    def _check_processed_status(self, query: str) -> str:
        """Check if a file has been processed."""
        try:
            # Extract file ID
            file_id = None
            if "file_id:" in query:
                file_id = query.split("file_id:")[1].strip().split()[0]
            elif "id:" in query:
                file_id = query.split("id:")[1].strip().split()[0]
            
            if not file_id:
                return "Please provide a file ID to check processing status."
            
            # Check for processing markers (this would integrate with your database)
            # For now, return a placeholder response
            result = {
                "status": "success",
                "file_id": file_id,
                "processed": False,
                "message": "File processing status check - integrate with database for actual status"
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error checking processed status: {e}")
            return f"Error checking file status: {str(e)}"
