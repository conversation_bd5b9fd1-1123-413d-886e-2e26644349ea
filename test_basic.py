import os
import sys

# Set up environment
os.environ['PYTHONPATH'] = './meeting-intelligence-agent'
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = './meeting-intelligence-agent/keys/google-service-account.json'
os.environ['GMAIL_CREDENTIALS_PATH'] = './meeting-intelligence-agent/keys/gmail-credentials.json'

def test_basic_functionality():
    """Test basic functionality without AI models."""
    try:
        # Test imports
        from src.utility.google_auth import GoogleAuthenticator
        from src.tools.langchain_calendar_tool import CalendarTool
        from src.tools.langchain_drive_tool import DriveTool
        
        print("✅ Basic imports successful")
        
        # Test Google authentication
        auth = GoogleAuthenticator()
        print("✅ Google authentication initialized")
        
        # Test calendar tool
        calendar_tool = CalendarTool(auth=auth)
        print("✅ Calendar tool initialized")
        
        # Test drive tool
        drive_tool = DriveTool(auth=auth)
        print("✅ Drive tool initialized")
        
        print("\n🎉 Basic functionality test passed!")
        print("\nNext steps:")
        print("1. Enable Vertex AI Generative AI in your Google Cloud Console")
        print("2. Or get a Gemini API key from https://makersuite.google.com/app/apikey")
        print("3. Set GEMINI_API_KEY environment variable")
        print("4. Run the full agent again")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_basic_functionality() 