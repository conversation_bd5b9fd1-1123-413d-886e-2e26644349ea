﻿"""
LangChain Agent Router for Post meeting Intelligence Workflow

Streamlined Workflow:
1. Identify Meeting & Transcript
2. Summarize Transcript (AI)
3. Generate JSON & HTML Summaries
4. Email Summaries to Attendees
5. Store Summaries in Google Drive

Cron job every 30 minutes triggers this workflow.
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
from uuid import uuid4
import schedule
import time
import threading

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Core LangChain agent
from src.agents.langchain_meeting_agent import (
    LangChainMeetingAgent, 
    get_langchain_agent, 
    run_autonomous_meeting_workflow,
    chat_with_meeting_agent
)

# Import app constants
from src.constants.app import API_PREFIX, RESPONSE_STATUS, HTTPStatus, HEALTH_CHECK_ENDPOINTS

# API models
from src.api.models import (
    TriggerRequest, TriggerResponse, StatusResponse,
    ConfigResponse, ErrorResponse, HealthResponse
)

logger = logging.getLogger(__name__)

# Create router (no prefix here since main.py adds /agent prefix)
router = APIRouter()

# Global instances
_langchain_agent: Optional[LangChainMeetingAgent] = None
_scheduler_running = False
_scheduler_thread = None

class MeetingProcessorRequest(BaseModel):
    """Request model for meeting processing."""
    time_window_minutes: int = Field(30, description="Time window for calendar events (minutes)")
    force_reprocess: bool = Field(False, description="Force reprocess already processed events")

def get_langchain_agent_instance() -> LangChainMeetingAgent:
    """Get or create LangChain agent instance."""
    global _langchain_agent
    if _langchain_agent is None:
        _langchain_agent = get_langchain_agent()
    return _langchain_agent

async def process_meeting_intelligence_workflow():
    """
    Post meeting Intelligence Workflow:
    
    1. Identify Meeting & Transcript
    2. Summarize Transcript (AI)
    3. Generate JSON & HTML Summaries
    4. Email Summaries to Attendees
    5. Store Summaries in Google Drive
    """
    try:
        logger.info("Starting Post meeting intelligence workflow")
        
        # Get LangChain agent
        agent = get_langchain_agent_instance()
        
        # Define the specific Post meetingworkflow task
        workflow_task = """
        Execute the Post meeting Intelligence Workflow:
        
        STEP 1: IDENTIFY MEETING & TRANSCRIPT
        - Use calendar_tool to find meetings from the last 30 minutes
        - Use drive_tool to locate transcript files for each meeting
        - Match transcripts to meetings based on title, time, and attendees
        
        STEP 2: SUMMARIZE TRANSCRIPT (AI)
        - Use summarizer_tool to process each transcript with AI
        - Generate comprehensive meeting summaries with key insights
        - Extract action items, decisions, and important discussion points
        
        STEP 3: GENERATE JSON & HTML SUMMARIES
        - Create professional HTML summaries for email distribution
        - Generate structured JSON summaries for data storage
        - Ensure both formats contain complete meeting information
        
        STEP 4: EMAIL SUMMARIES TO ATTENDEES
        - Use notification_tool to send HTML summaries to all meeting attendees
        - Include meeting details, key outcomes, and action items
        - Personalize emails with attendee-specific information
        
        STEP 5: STORE SUMMARIES IN GOOGLE DRIVE
        - Use drive_tool to organize summaries in structured folders
        - Store both JSON and HTML versions in appropriate directories
        - Maintain organized file structure for easy retrieval
        
        WORKFLOW REQUIREMENTS:
        - Process only meetings from the last 30 minutes
        - Ensure no duplicate processing of the same meetings
        - Handle errors gracefully and continue with remaining meetings
        - Provide detailed logging of each step
        - Maintain professional quality in all outputs
        
        Execute this workflow autonomously using your available tools.
        """
        
        # Execute the workflow
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        result = await run_autonomous_meeting_workflow(task_description=workflow_task)
        
        logger.info("Post meeting intelligence workflow completed")
        return result
        
    except Exception as e:
        logger.error(f"Workflow execution failed: {e}")
        raise e

def run_scheduler():
    """Run the scheduler in a separate thread."""
    global _scheduler_running
    _scheduler_running = True
    
    logger.info("📅 Starting 30-minute scheduler for meeting intelligence workflow")
    
    # Schedule the workflow every 30 minutes
    schedule.every(30).minutes.do(lambda: asyncio.run(process_meeting_intelligence_workflow()))
    
    while _scheduler_running:
        schedule.run_pending()
        time.sleep(60)  # Check every minute
    
    logger.info("📅 Scheduler stopped")

# API Endpoints

@router.post("/start-scheduler")
async def start_scheduler():
    """Start the 30-minute automated scheduler."""
    global _scheduler_thread, _scheduler_running
    
    if _scheduler_running:
        return {"status": "already_running", "message": "Scheduler is already running"}
    
    try:
        _scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        _scheduler_thread.start()
        
        logger.info("🚀 30-minute scheduler started successfully")
        return {
            "status": "started",
            "interval": "30 minutes",
            "workflow": "Post meeting intelligence",
            "next_run": (datetime.now() + timedelta(minutes=30)).isoformat(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to start scheduler: {e}")
        raise HTTPException(status_code=500, detail=f"Scheduler start failed: {str(e)}")

@router.post("/stop-scheduler")
async def stop_scheduler():
    """Stop the automated scheduler."""
    global _scheduler_running
    
    if not _scheduler_running:
        return {"status": "not_running", "message": "Scheduler is not running"}
    
    _scheduler_running = False
    schedule.clear()
    
    logger.info("📅 Scheduler stopped")
    return {
        "status": "stopped",
        "timestamp": datetime.now().isoformat()
    }

@router.get("/scheduler-status")
async def get_scheduler_status():
    """Get current scheduler status."""
    return {
        "running": _scheduler_running,
        "interval": "30 minutes" if _scheduler_running else "stopped",
        "workflow": "Post meeting intelligence",
        "next_run": (datetime.now() + timedelta(minutes=30)).isoformat() if _scheduler_running else None,
        "timestamp": datetime.now().isoformat()
    }

@router.post("/trigger-workflow")
async def trigger_workflow_manually():
    """Manually trigger the Post meetingworkflow."""
    try:
        logger.info("🎯 Manual workflow trigger requested")
        
        result = await process_meeting_intelligence_workflow()
        
        return {
            "status": "success",
            "message": "Post meetingworkflow completed successfully",
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Manual workflow trigger failed: {e}")
        raise HTTPException(status_code=500, detail=f"Workflow failed: {str(e)}")

@router.get("/workflow-status")
async def get_workflow_status():
    """Get current workflow status and information."""
    try:
        agent = get_langchain_agent_instance()
        
        return {
            "workflow": "Post meeting intelligence",
            "steps": [
                "1. Identify Meeting & Transcript",
                "2. Summarize Transcript (AI)",
                "3. Generate JSON & HTML Summaries",
                "4. Email Summaries to Attendees",
                "5. Store Summaries in Google Drive"
            ],
            "agent_status": "active",
            "scheduler_running": _scheduler_running,
            "tools_available": [tool.name for tool in agent.tools],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Workflow status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")

@router.post("/chat")
async def chat_with_agent(message: dict):
    """Chat with the LangChain agent."""
    try:
        user_message = message.get("message", "")
        if not user_message:
            raise HTTPException(status_code=400, detail="Message is required")
        
        response = await chat_with_meeting_agent(user_message)
        
        return {
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Chat failed: {e}")
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")

@router.get("/health")
async def health_check():
    """Health check for the agent router."""
    try:
        agent = get_langchain_agent_instance()
        
        return {
            "status": "healthy",
            "agent": "LangChain Meeting Intelligence Agent",
            "workflow": "Post meeting intelligence",
            "scheduler": "running" if _scheduler_running else "stopped",
            "tools": len(agent.tools),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )
